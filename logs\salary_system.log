2025-08-31 12:50:49.051 | INFO     | src.utils.log_config:_log_initialization_info:289 | 日志系统初始化完成
2025-08-31 12:50:49.051 | INFO     | src.utils.log_config:_log_initialization_info:290 | 日志级别: INFO
2025-08-31 12:50:49.051 | INFO     | src.utils.log_config:_log_initialization_info:291 | 控制台输出: True
2025-08-31 12:50:49.051 | INFO     | src.utils.log_config:_log_initialization_info:292 | 文件输出: True
2025-08-31 12:50:49.056 | INFO     | src.utils.log_config:_log_initialization_info:298 | 日志文件路径: logs/salary_system.log
2025-08-31 12:50:49.056 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-08-31 12:50:52.136 | INFO     | src.gui.style_manager:__init__:68 | StyleManager初始化完成
2025-08-31 12:50:52.136 | INFO     | src.gui.style_manager:apply_global_style:93 | 开始应用全局Material Design样式...
2025-08-31 12:50:52.136 | INFO     | src.gui.style_manager:apply_global_style:105 | 全局样式应用成功
2025-08-31 12:50:52.136 | INFO     | src.gui.style_manager:enable_hot_reload:416 | 样式热重载未启用（非开发环境）
2025-08-31 12:50:52.136 | INFO     | __main__:setup_app_logging:423 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-08-31 12:50:52.136 | INFO     | __main__:main:487 | 初始化核心管理器...
2025-08-31 12:50:52.136 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-31 12:50:52.143 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-08-31 12:50:52.143 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-08-31 12:50:52.143 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-08-31 12:50:52.143 | INFO     | src.modules.data_storage.database_manager:_initialize_database:102 | 开始初始化数据库...
2025-08-31 12:50:52.155 | INFO     | src.modules.data_storage.database_manager:_initialize_database:157 | 数据库初始化完成
2025-08-31 12:50:52.155 | INFO     | src.modules.data_storage.database_manager:__init__:98 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-08-31 12:50:52.157 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-31 12:50:52.163 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:107 | 动态表管理器初始化完成
2025-08-31 12:50:52.163 | INFO     | __main__:main:492 | 核心管理器初始化完成。
2025-08-31 12:50:52.163 | INFO     | src.core.data_source_unification_manager:__init__:128 | 数据源统一管理器初始化完成
2025-08-31 12:50:52.163 | INFO     | src.core.table_sort_state_manager:__init__:177 | 表级排序状态管理器初始化完成
2025-08-31 12:50:52.163 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-31 12:50:52.163 | INFO     | src.core.error_handler_manager:__init__:111 | 🔧 [P2-3] 错误处理管理器初始化完成
2025-08-31 12:50:52.163 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: data_error_medium
2025-08-31 12:50:52.169 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: ui_error_medium
2025-08-31 12:50:52.170 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: system_error_medium
2025-08-31 12:50:52.176 | INFO     | src.gui.prototype.prototype_main_window:_register_recovery_strategies:11899 | 🔧 [P2-3] 错误恢复策略注册完成
2025-08-31 12:50:52.176 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-31 12:50:52.176 | INFO     | src.gui.prototype.prototype_main_window:_setup_error_handling:11754 | 🔧 [P2-3] 错误处理机制设置完成
2025-08-31 12:50:52.176 | INFO     | src.gui.prototype.prototype_main_window:_init_recursive_call_protection:11792 | 🔧 [P1-2] 递归调用防护机制初始化完成
2025-08-31 12:50:52.357 | INFO     | src.core.architecture_factory:__init__:62 | 架构重构工厂初始化完成
2025-08-31 12:50:52.363 | INFO     | src.core.architecture_factory:initialize_architecture:72 | 开始初始化架构重构系统...
2025-08-31 12:50:52.363 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-31 12:50:52.363 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:153 | 使用已存在的配置文件: state/data/field_mappings.json
2025-08-31 12:50:52.363 | INFO     | src.modules.data_import.config_sync_manager:__init__:81 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-08-31 12:50:52.370 | INFO     | src.core.unified_state_manager:_load_state:585 | 状态已从文件加载: state/unified_state.json
2025-08-31 12:50:52.371 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-31 12:50:52.371 | INFO     | src.core.field_mapping_manager:_load_config:89 | 🔧 [P3优化] 字段映射配置加载成功
2025-08-31 12:50:52.371 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:153 | 使用已存在的配置文件: state/data/field_mappings.json
2025-08-31 12:50:52.371 | INFO     | src.modules.data_import.config_sync_manager:__init__:81 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-08-31 12:50:52.371 | INFO     | src.modules.format_management.field_registry:__init__:95 | 🏷️ [字段注册] 字段注册系统初始化: state/data/field_mappings.json
2025-08-31 12:50:52.377 | INFO     | src.core.unified_mapping_service:__init__:45 | UnifiedMappingService 初始化完成
2025-08-31 12:50:52.377 | INFO     | src.core.unified_data_request_manager:__init__:217 | 统一数据请求管理器初始化完成
2025-08-31 12:50:52.377 | INFO     | src.core.unified_data_request_manager:__init__:217 | 统一数据请求管理器初始化完成
2025-08-31 12:50:52.380 | INFO     | src.core.unified_state_manager:_load_state:585 | 状态已从文件加载: state/unified_state.json
2025-08-31 12:50:52.380 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-31 12:50:52.380 | INFO     | src.services.table_data_service:__init__:84 | 表格数据服务初始化完成
2025-08-31 12:50:52.380 | INFO     | src.core.architecture_factory:initialize_architecture:103 | 🎉 架构重构系统初始化成功！耗时: 16.9ms
2025-08-31 12:50:52.413 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:46 | 数据预加载缓存初始化完成 - 最大条目数: 100, TTL: 300秒
2025-08-31 12:50:52.413 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:232 | 表格状态缓存初始化完成
2025-08-31 12:50:52.413 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:32 | 表头配置缓存初始化完成
2025-08-31 12:50:52.413 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:211 | 字段映射缓存初始化完成
2025-08-31 12:50:52.413 | INFO     | src.gui.prototype.performance.performance_manager:__init__:46 | 🚀 性能管理器初始化完成
2025-08-31 12:50:52.413 | INFO     | src.gui.prototype.prototype_main_window:__init__:3650 | 🚀 性能管理器已集成
2025-08-31 12:50:52.413 | INFO     | src.gui.prototype.prototype_main_window:__init__:3652 | ✅ 新架构集成成功！
2025-08-31 12:50:52.413 | INFO     | src.gui.prototype.prototype_main_window:_inject_config_sync_manager_to_existing_tables:3765 | ConfigSyncManager重新注入完成，已更新0个表格实例
2025-08-31 12:50:52.413 | INFO     | src.gui.prototype.prototype_main_window:_setup_new_architecture_listeners:3730 | ✅ 新架构事件监听器设置完成
2025-08-31 12:50:52.420 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-08-31 12:50:52.429 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-08-31 12:50:52.429 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-08-31 12:50:52.685 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:2726 | 菜单栏创建完成
2025-08-31 12:50:52.685 | INFO     | src.gui.prototype.prototype_main_window:__init__:2701 | 菜单栏管理器初始化完成
2025-08-31 12:50:52.697 | INFO     | src.gui.table_header_manager:__init__:105 | 🔧 [P1-3] 性能优化表头管理器初始化完成
2025-08-31 12:50:52.698 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:5458 | 管理器设置完成，包含增强版表头管理器
2025-08-31 12:50:52.698 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5463 | 🔧 开始应用窗口级Material Design样式...
2025-08-31 12:50:52.698 | INFO     | src.gui.style_manager:apply_window_style:123 | 开始应用窗口级样式...
2025-08-31 12:50:52.701 | INFO     | src.gui.style_manager:apply_window_style:136 | 窗口级样式应用成功
2025-08-31 12:50:52.701 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5470 | ✅ 窗口级样式应用成功
2025-08-31 12:50:52.701 | INFO     | src.gui.prototype.prototype_main_window:_setup_responsive_style:5511 | ✅ 响应式样式监听设置完成
2025-08-31 12:50:52.707 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-08-31 12:50:52.707 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-08-31 12:50:52.707 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-08-31 12:50:52.707 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_state:165 | 导航状态加载成功
2025-08-31 12:50:52.713 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-08-31 12:50:52.713 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-08-31 12:50:52.730 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-31 12:50:52.730 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1992 | 元数据项 salary_data_2025_05 缺少年份或月份信息，已跳过
2025-08-31 12:50:52.735 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_fallback_salary_data:1835 | 🏠 使用示例数据结构加载导航（首次启动或暂无数据时显示）
2025-08-31 12:50:52.735 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 4 个匹配类型 'change_data' 的表 (尝试 1/5)
2025-08-31 12:50:52.735 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-31 12:50:52.735 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:924 | 恢复导航状态: 4个展开项
2025-08-31 12:50:52.735 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > 全部在职人员', '工资表', '工资表 > 2025年', '工资表 > 2025年 > 05月']
2025-08-31 12:50:52.744 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-08-31 12:50:52.751 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_state:165 | 导航状态加载成功
2025-08-31 12:50:52.753 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-08-31 12:50:52.758 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-08-31 12:50:52.764 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-31 12:50:52.765 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1992 | 元数据项 salary_data_2025_05 缺少年份或月份信息，已跳过
2025-08-31 12:50:52.771 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 4 个匹配类型 'change_data' 的表 (尝试 1/5)
2025-08-31 12:50:52.776 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-31 12:50:52.785 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['异动人员表 > 2025年 > 12月 > A岗职工', '异动人员表 > 2025年 > 12月 > 退休人员工资表', '异动人员表 > 2025年 > 12月 > 离休人员工资表', '异动人员表 > 2025年 > 12月', '异动人员表 > 2025年']
2025-08-31 12:50:52.818 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['异动人员表 > 2025年 > 12月 > A岗职工', '异动人员表 > 2025年 > 12月 > 退休人员工资表', '异动人员表 > 2025年 > 12月 > 离休人员工资表', '异动人员表 > 2025年 > 12月', '异动人员表 > 2025年']
2025-08-31 12:50:52.820 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-08-31 12:50:52.821 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1396 | 🔧 [P1-2修复] 开始自动选择最新数据...
2025-08-31 12:50:52.823 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-08-31 12:50:52.827 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-31 12:50:52.832 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2173 | 未找到有效的工资数据表
2025-08-31 12:50:52.833 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1601 | 🔧 [P1-2修复] 检查表状态失败，1.0s后重试...
2025-08-31 12:50:52.836 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1405 | 🔧 [P1-2修复] 未找到最新工资数据路径（可能是首次启动）
2025-08-31 12:50:53.134 | INFO     | src.gui.prototype.widgets.header_update_manager:__init__:68 | HeaderUpdateManager 初始化完成，关联表格: VirtualizedExpandableTable
2025-08-31 12:50:53.136 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2142 | 🚨 [架构修复] 启用模型数据同步机制
2025-08-31 12:50:53.141 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1366 | 快捷键注册完成: 18/18 个
2025-08-31 12:50:53.141 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1809 | 拖拽排序管理器初始化完成
2025-08-31 12:50:53.165 | INFO     | src.modules.data_management.data_flow_validator:__init__:85 | 🔧 [数据验证器] 初始化完成，验证级别: lenient
2025-08-31 12:50:53.165 | INFO     | src.modules.state_management.table_state_manager:__init__:103 | 🔧 [状态管理] 仅使用内存缓存
2025-08-31 12:50:53.168 | INFO     | src.modules.state_management.table_state_manager:__init__:105 | 🔧 [状态管理] 初始化完成，最大缓存: 100
2025-08-31 12:50:53.169 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2195 | 🔧 [P0-紧急修复] 数据流验证器初始化成功（宽松模式）
2025-08-31 12:50:53.170 | INFO     | src.modules.data_import.header_edit_manager:__init__:82 | 表头编辑管理器初始化完成
2025-08-31 12:50:53.171 | INFO     | src.core.unified_state_manager:_load_state:585 | 状态已从文件加载: state/unified_state.json
2025-08-31 12:50:53.171 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-31 12:50:53.172 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2247 | 🎯 [统一格式管理] 使用统一格式管理器
2025-08-31 12:50:53.187 | INFO     | src.gui.prototype.widgets.column_sort_manager:_load_field_mappings_from_config:370 | 🔧 [新架构] 成功加载 46 个字段映射
2025-08-31 12:50:53.187 | INFO     | src.gui.prototype.widgets.column_sort_manager:__init__:102 | 🆕 [新架构排序] 多列排序管理器初始化完成，最大排序列数: 3
2025-08-31 12:50:53.189 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2294 | 🆕 [新架构多列排序] 初始化完成，支持最多3列排序
2025-08-31 12:50:53.190 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1553 | 🔧 [列宽保存修复] 列宽管理器初始化完成
2025-08-31 12:50:53.191 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1554 | 🔧 [列宽保存修复] 配置文件绝对路径: C:\test\salary_changes\salary_changes\state\column_widths.json
2025-08-31 12:50:53.192 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1555 | 🔧 [列宽保存修复] 配置文件存在: True
2025-08-31 12:50:53.194 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1556 | 🔧 [列宽保存修复] 父目录存在: True
2025-08-31 12:50:53.200 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1557 | 🔧 [列宽保存修复] 当前工作目录: C:\test\salary_changes\salary_changes
2025-08-31 12:50:53.201 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2301 | 列宽管理器初始化完成
2025-08-31 12:50:53.202 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:setup_ui:2428 | 🔧 [架构修复] 保持自定义排序功能，修复数据显示问题
2025-08-31 12:50:53.205 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2315 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-08-31 12:50:53.205 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2306 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-31 12:50:53.206 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2736 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-31 12:50:53.207 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4669 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-31 12:50:53.226 | INFO     | src.gui.prototype.widgets.pagination_state_manager:__init__:78 | 🔧 [P1优化] 分页状态管理器初始化完成
2025-08-31 12:50:53.227 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7339 | 方案A：安全设置列数: 0
2025-08-31 12:50:53.231 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2838 | 🔧 [P1优化] 表切换操作: None -> default_table
2025-08-31 12:50:53.232 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5573 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-31 12:50:53.244 | INFO     | src.gui.prototype.widgets.table_header_cache:__init__:67 | 🔧 [P1优化] 表头缓存管理器初始化完成，最大缓存: 100
2025-08-31 12:50:53.253 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2890 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-31 12:50:53.253 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2975 | 表格格式化完成: default_table, 类型: active_employees
2025-08-31 12:50:53.266 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-31 12:50:53.268 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-31 12:50:53.269 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-31 12:50:53.271 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-31 12:50:53.286 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:443 | 表格数据已设置: 0 行, 22 列
2025-08-31 12:50:53.294 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:443 | 🔧 [新架构] 排序管理器切换到表格: default_table
2025-08-31 12:50:53.296 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3159 | 表格数据设置完成: 0 行, 耗时: 75.6ms
2025-08-31 12:50:53.297 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8277 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-31 12:50:53.298 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8290 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-31 12:50:53.308 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-31 12:50:53.312 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3238 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-31 12:50:53.313 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2331 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-31 12:50:53.323 | INFO     | src.gui.widgets.pagination_widget:_apply_styles:298 | 分页组件Material Design样式应用成功
2025-08-31 12:50:53.336 | INFO     | src.gui.widgets.pagination_widget:__init__:176 | ✅ [防抖升级] 智能防抖系统已启用
2025-08-31 12:50:53.337 | INFO     | src.gui.widgets.pagination_widget:__init__:184 | 分页组件初始化完成
2025-08-31 12:50:53.384 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:594 | 控制面板按钮信号连接完成
2025-08-31 12:50:53.436 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:5420 | 快捷键设置完成
2025-08-31 12:50:53.437 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:5377 | 主窗口UI设置完成。
2025-08-31 12:50:53.439 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5614 | 🔧 [全局排序] 全局排序开关连接成功
2025-08-31 12:50:53.440 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5646 | 🆕 [新架构排序] 使用表格组件内部的自定义排序循环，无需连接排序信号
2025-08-31 12:50:53.441 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5658 | ✅ 已连接分页刷新信号到主窗口
2025-08-31 12:50:53.441 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5659 | ✅ 已连接分页组件事件到新架构
2025-08-31 12:50:53.443 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5670 | ✅ 已连接概览标签页刷新按钮到主窗口
2025-08-31 12:50:53.444 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5673 | 信号连接设置完成
2025-08-31 12:50:53.445 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6931 | 🔧 [P1-2修复] 发现 6 个表的配置
2025-08-31 12:50:53.447 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:488 | 🔧 [P1-1修复] 加载统一格式映射: salary_data_2025_07_active_employees, 10个字段
2025-08-31 12:50:53.448 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:488 | 🔧 [P1-1修复] 加载统一格式映射: active_employees, 5个字段
2025-08-31 12:50:53.454 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: change_data_2025_12_全部在职人员工资表, 30个字段
2025-08-31 12:50:53.455 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: change_data_2025_12_退休人员工资表, 30个字段
2025-08-31 12:50:53.456 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: change_data_2025_12_A岗职工, 30个字段
2025-08-31 12:50:53.457 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: change_data_2025_12_离休人员工资表, 30个字段
2025-08-31 12:50:53.457 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6941 | ✅ [P1-2修复] 已加载字段映射信息，共6个表的映射
2025-08-31 12:50:53.466 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2306 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-31 12:50:53.467 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2736 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-31 12:50:53.468 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4669 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-31 12:50:53.469 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5573 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-31 12:50:53.470 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2975 | 表格格式化完成: default_table, 类型: active_employees
2025-08-31 12:50:53.471 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7339 | 方案A：安全设置列数: 22
2025-08-31 12:50:53.471 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-31 12:50:53.472 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-31 12:50:53.473 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-31 12:50:53.474 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-31 12:50:53.476 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3159 | 表格数据设置完成: 0 行, 耗时: 6.8ms
2025-08-31 12:50:53.481 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8277 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-31 12:50:53.482 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8290 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-31 12:50:53.484 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-31 12:50:53.486 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3238 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-31 12:50:53.488 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2331 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-31 12:50:53.489 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-31 12:50:53.490 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8633 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 22个字段
2025-08-31 12:50:53.496 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2306 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-31 12:50:53.496 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2736 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-31 12:50:53.497 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4669 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-31 12:50:53.498 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5573 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-31 12:50:53.499 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2975 | 表格格式化完成: default_table, 类型: active_employees
2025-08-31 12:50:53.500 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-31 12:50:53.501 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-31 12:50:53.502 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-31 12:50:53.503 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-31 12:50:53.509 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3159 | 表格数据设置完成: 0 行, 耗时: 11.5ms
2025-08-31 12:50:53.509 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8277 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-31 12:50:53.510 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8290 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-31 12:50:53.512 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-31 12:50:53.512 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3238 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-31 12:50:53.516 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2331 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-31 12:50:53.543 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8651 | 已显示标准空表格，表头数量: 22
2025-08-31 12:50:53.588 | INFO     | src.gui.prototype.prototype_main_window:__init__:3704 | 原型主窗口初始化完成
2025-08-31 12:50:53.705 | INFO     | __main__:main:514 | 应用程序启动成功
2025-08-31 12:50:53.714 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8312 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-31 12:50:53.714 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1474 | 执行延迟的自动选择最新数据...
2025-08-31 12:50:53.715 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8312 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-31 12:50:53.716 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8312 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-31 12:50:53.811 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1280px)
2025-08-31 12:50:53.816 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2079 | MainWorkspaceArea 响应式适配: sm
2025-08-31 12:50:53.877 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-08-31 12:50:53.880 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-31 12:50:53.881 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2173 | 未找到有效的工资数据表
2025-08-31 12:50:54.335 | INFO     | src.gui.prototype.prototype_main_window:delayed_heavy_fixes:9535 | UI亮度问题检测到并修复: ['table_opacity_in_stylesheet']
2025-08-31 12:50:54.336 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9445 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-08-31 12:50:54.340 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9459 | 🔧 [P0-1] 检测到亮度问题: ['table_opacity_in_stylesheet']
2025-08-31 12:50:54.340 | INFO     | src.gui.prototype.prototype_main_window:_apply_brightness_fixes:9993 | 🔧 [P1-2] 开始应用增强版亮度修复: ['table_opacity_in_stylesheet']
2025-08-31 12:50:54.357 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9465 | 🔧 [P0-1] 智能显示亮度修复完成
2025-08-31 12:50:54.883 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1396 | 🔧 [P1-2修复] 开始自动选择最新数据...
2025-08-31 12:50:54.883 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-08-31 12:50:54.886 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-31 12:50:54.886 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2173 | 未找到有效的工资数据表
2025-08-31 12:50:55.886 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-08-31 12:50:55.888 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-31 12:50:55.889 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2173 | 未找到有效的工资数据表
2025-08-31 12:50:55.891 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1612 | 🔧 [P1-2修复] 3次重试已用尽，可能数据导入尚未完成
2025-08-31 12:51:02.276 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:654 | 数据导入功能被触发，发出 import_requested 信号。
2025-08-31 12:51:02.277 | INFO     | src.gui.prototype.prototype_main_window:_get_suggested_target_path:8456 | 检测到当前在工资表TAB，生成工资表默认路径
2025-08-31 12:51:02.280 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:5906 | 接收到数据导入请求，推断的目标路径: 工资表 > 2025年 > 08月 > 全部在职人员。打开导入对话框。
2025-08-31 12:51:02 - src.gui.unified_data_import_window - INFO - 初始化统一数据导入窗口
2025-08-31 12:51:02.350 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-31 12:51:02.353 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-08-31 12:51:02.354 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-08-31 12:51:02.354 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-08-31 12:51:02.355 | INFO     | src.modules.data_storage.database_manager:_initialize_database:102 | 开始初始化数据库...
2025-08-31 12:51:02.362 | INFO     | src.modules.data_storage.database_manager:_initialize_database:157 | 数据库初始化完成
2025-08-31 12:51:02.364 | INFO     | src.modules.data_storage.database_manager:__init__:98 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-08-31 12:51:02.365 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-31 12:51:02.374 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:107 | 动态表管理器初始化完成
2025-08-31 12:51:02.376 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-08-31 12:51:02.379 | INFO     | src.core.config_cache_manager:__init__:67 | 配置缓存管理器初始化完成: cache\config_cache, 最大条目数: 100
2025-08-31 12:51:02.379 | INFO     | src.modules.data_import.change_data_config_manager:__init__:69 | 配置缓存已启用
2025-08-31 12:51:02.380 | INFO     | src.modules.data_import.change_data_config_manager:__init__:75 | 配置管理器初始化完成，配置目录: C:\test\salary_changes\salary_changes\state\change_data_configs
2025-08-31 12:51:02.382 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:78 | 多Sheet导入器初始化完成
2025-08-31 12:51:02 - src.gui.core.smart_mapping_engine - INFO - 成功加载历史配置: 3 个配置
2025-08-31 12:51:02 - src.gui.core.smart_mapping_engine - INFO - 智能映射引擎初始化完成
2025-08-31 12:51:02 - src.gui.core.template_manager - INFO - 模板管理器初始化完成: 内置模板 3 个, 用户模板 0 个
2025-08-31 12:51:02 - src.gui.core.validation_engine - INFO - 验证引擎初始化完成
2025-08-31 12:51:02 - src.gui.unified_data_import_window - INFO - 统一导入管理器初始化完成
2025-08-31 12:51:02.441 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-31 12:51:02 - src.gui.unified_data_import_window - INFO - 核心组件初始化成功
2025-08-31 12:51:02 - src.gui.unified_data_import_window - INFO - 接收到高级配置变化: {'file_import': {'default_import_path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'supported_formats': ['xlsx', 'xls', 'csv'], 'max_file_size_mb': 105, 'auto_detect_encoding': True, 'sheet_selection_strategy': 'all', 'data_start_row': 2, 'header_row': 1, 'skip_rows': 0, 'skip_footer_rows': 0, 'auto_detect_header': True, 'ignore_empty_rows': True}, 'field_mapping': {'mapping_algorithm': 'fuzzy_match', 'similarity_threshold': 80, 'auto_mapping_enabled': True, 'required_field_check': True, 'field_type_validation': True, 'save_mapping_history': True}, 'smart_recommendations': {'confidence_threshold': 70, 'enable_history_learning': True, 'enable_semantic_analysis': True, 'auto_apply_high_confidence': False, 'template_priority': 0, 'max_saved_templates': 50}, 'data_processing': {'strict_validation': False, 'null_value_strategy': 0, 'auto_type_conversion': True, 'duplicate_strategy': 0, 'batch_size': 1000, 'error_tolerance': 10}, 'ui_customization': {'table_row_limit': 200, 'show_detailed_logs': False, 'show_confidence_indicators': True, 'auto_save_interval': 5, 'show_confirmation_dialogs': True, 'show_shortcuts': True}, 'performance': {'max_memory_usage': 2048, 'enable_caching': True, 'preload_data': False, 'thread_count': 4, 'enable_async_processing': True, 'progress_update_frequency': 100}}
2025-08-31 12:51:02 - src.gui.unified_data_import_window - INFO - 应用默认导入文件: C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls
2025-08-31 12:51:02 - src.gui.unified_data_import_window - INFO - UI未初始化，保存默认路径供后续应用: C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls
2025-08-31 12:51:02 - src.gui.unified_data_import_window - INFO - 文件导入配置已应用，包括Excel表结构设置: {'data_start_row': 2, 'header_row': 1, 'skip_rows': 0, 'skip_footer_rows': 0, 'auto_detect_header': True, 'ignore_empty_rows': True}
2025-08-31 12:51:02 - src.gui.unified_data_import_window - INFO - 字段映射配置已应用
2025-08-31 12:51:02 - src.gui.unified_data_import_window - INFO - 智能推荐配置已应用
2025-08-31 12:51:02 - src.gui.unified_data_import_window - INFO - 数据处理配置已应用
2025-08-31 12:51:02 - src.gui.unified_data_import_window - INFO - 界面个性化配置已应用
2025-08-31 12:51:02 - src.gui.unified_data_import_window - INFO - 性能优化配置已应用
2025-08-31 12:51:02 - src.gui.unified_data_import_window - INFO - 所有高级配置域已成功应用: ['file_import', 'field_mapping', 'smart_recommendations', 'data_processing', 'ui_customization', 'performance']
2025-08-31 12:51:02 - src.gui.unified_data_import_window - INFO - 🔧 [P0修复] 已加载保存的高级配置
2025-08-31 12:51:02 - src.gui.core.smart_mapping_engine - INFO - 成功加载历史配置: 3 个配置
2025-08-31 12:51:02 - src.gui.core.smart_mapping_engine - INFO - 智能映射引擎初始化完成
2025-08-31 12:51:02 - src.gui.core.template_manager - INFO - 模板管理器初始化完成: 内置模板 3 个, 用户模板 0 个
2025-08-31 12:51:02 - src.gui.core.validation_engine - INFO - 验证引擎初始化完成
2025-08-31 12:51:02 - src.gui.performance_optimizer - INFO - 性能优化器初始化完成
2025-08-31 12:51:02 - src.gui.unified_data_import_window - INFO - 响应式分割器设置完成: 左侧=308px, 右侧=1092px
2025-08-31 12:51:02 - src.gui.unified_data_import_window - INFO - UI界面创建完成
2025-08-31 12:51:02 - src.gui.unified_data_import_window - INFO - 应用待处理的默认文件: C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls
2025-08-31 12:51:02.791 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:178 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-08-31 12:51:02.792 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-08-31 12:51:02.795 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-08-31 12:51:02.795 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-31 12:51:02.907 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 16列 (列过滤: 否)
2025-08-31 12:51:02.910 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-31 12:51:02.912 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-08-31 12:51:02.913 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 3行 x 16列
2025-08-31 12:51:02.916 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:599 | 数据质量检查: 已过滤1条姓名为空的记录
2025-08-31 12:51:02.917 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:642 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-08-31 12:51:02.924 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 2行 × 16列
2025-08-31 12:51:02.925 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-31 12:51:03.037 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-31 12:51:03.039 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 27 列 (原始 27 列)
2025-08-31 12:51:03.042 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-31 12:51:03.151 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-31 12:51:03.151 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 23 列 (原始 23 列)
2025-08-31 12:51:03.157 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-31 12:51:03.258 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-31 12:51:03.258 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-08-31 12:51:03 - src.gui.unified_data_import_window - INFO - 已加载 4 个Sheet
2025-08-31 12:51:03 - src.gui.unified_data_import_window - INFO - 成功加载 4 个工作表
2025-08-31 12:51:03 - src.gui.unified_data_import_window - INFO - 待处理配置应用完成: C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls
2025-08-31 12:51:03 - src.gui.unified_data_import_window - INFO - 信号连接完成
2025-08-31 12:51:03 - src.gui.unified_data_import_window - INFO - 统一数据导入窗口初始化完成
2025-08-31 12:51:03 - src.gui.unified_data_import_window - INFO - 响应式分割器设置完成: 左侧=308px, 右侧=1092px
2025-08-31 12:51:03 - src.gui.unified_data_import_window - INFO - 响应式布局初始化完成
2025-08-31 12:51:03 - src.gui.unified_data_import_window - INFO - 响应式分割器设置完成: 左侧=308px, 右侧=1092px
2025-08-31 12:51:09 - src.gui.unified_data_import_window - INFO - Sheet选择变化: 1 个选中
2025-08-31 12:51:09 - src.gui.unified_data_import_window - INFO - 开始加载Sheet字段: 离休人员工资表
2025-08-31 12:51:09.184 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-08-31 12:51:09.184 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-08-31 12:51:09.185 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-31 12:51:09.285 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 16列 (列过滤: 否)
2025-08-31 12:51:09.290 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-31 12:51:09.290 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-08-31 12:51:09.290 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 1行 x 16列
2025-08-31 12:51:09.297 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 1行 × 16列
2025-08-31 12:51:09 - src.gui.unified_data_import_window - INFO - 成功读取字段头: ['序号', '人员代码', '姓名', '部门名称', '基本\n离休费', '结余\n津贴', '生活\n补贴', '住房\n补贴', '物业\n补贴', '离休\n补贴', '护理费', '增发一次\n性生活补贴', '补发', '合计', '借支', '备注']
2025-08-31 12:51:09 - src.gui.unified_data_import_window - INFO - 加载Excel字段: 16 个字段, 表类型: 
2025-08-31 12:51:09 - src.gui.unified_data_import_window - INFO - 字段映射表格创建完成: 16 行
2025-08-31 12:51:09 - src.gui.unified_data_import_window - INFO - 成功加载字段: 离休人员工资表 - 16 个字段
2025-08-31 12:51:12 - src.gui.unified_data_import_window - INFO - Sheet选择变化: 0 个选中
2025-08-31 12:51:13 - src.gui.unified_data_import_window - INFO - Sheet选择变化: 1 个选中
2025-08-31 12:51:13 - src.gui.unified_data_import_window - INFO - 开始加载Sheet字段: A岗职工
2025-08-31 12:51:13.701 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-08-31 12:51:13.701 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-08-31 12:51:13.701 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-31 12:51:13.807 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 21列 (列过滤: 否)
2025-08-31 12:51:13.813 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-31 12:51:13.813 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-08-31 12:51:13.818 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 1行 x 21列
2025-08-31 12:51:13.818 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 1行 × 21列
2025-08-31 12:51:13 - src.gui.unified_data_import_window - INFO - 成功读取字段头: ['序号', '工号', '姓名', '部门名称', '人员类别', '人员类别代码', '2025年岗位工资', '2025年校龄工资', '津贴', '结余津贴', '2025年基础性绩效', '卫生费', '2025年生活补贴', '车补', '2025年奖励性绩效预发', '补发', '借支', '应发工资', '2025公积金', '保险扣款', '代扣代存养老保险']
2025-08-31 12:51:13 - src.gui.unified_data_import_window - INFO - 加载Excel字段: 21 个字段, 表类型: 
2025-08-31 12:51:13 - src.gui.unified_data_import_window - INFO - 字段映射表格创建完成: 21 行
2025-08-31 12:51:13 - src.gui.unified_data_import_window - INFO - 成功加载字段: A岗职工 - 21 个字段
2025-08-31 12:51:41 - src.gui.unified_data_import_window - INFO - 选中Sheet: 全部在职人员工资表
2025-08-31 12:51:42 - src.gui.unified_data_import_window - INFO - 选中Sheet: A岗职工
2025-08-31 12:51:47 - src.gui.unified_data_import_window - INFO - Sheet选择变化: 2 个选中
2025-08-31 12:51:49 - src.gui.unified_data_import_window - INFO - Sheet选择变化: 1 个选中
2025-08-31 12:51:49 - src.gui.unified_data_import_window - INFO - 开始加载Sheet字段: 全部在职人员工资表
2025-08-31 12:51:49.175 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-08-31 12:51:49.177 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-08-31 12:51:49.177 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-31 12:51:49.288 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 23列 (列过滤: 否)
2025-08-31 12:51:49.291 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-31 12:51:49.293 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 23 列 (原始 23 列)
2025-08-31 12:51:49.294 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 1行 x 23列
2025-08-31 12:51:49.298 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 1行 × 23列
2025-08-31 12:51:49 - src.gui.unified_data_import_window - INFO - 成功读取字段头: ['序号', '工号', '姓名', '部门名称', '人员类别代码', '人员类别', '2025年岗位工资', '2025年薪级工资', '津贴', '结余津贴', '2025年基础性绩效', '卫生费', '交通补贴', '物业补贴', '住房补贴', '车补', '通讯补贴', '2025年奖励性绩效预发', '补发', '借支', '应发工资', '2025公积金', '代扣代存养老保险']
2025-08-31 12:51:49 - src.gui.unified_data_import_window - INFO - 加载Excel字段: 23 个字段, 表类型: 
2025-08-31 12:51:49 - src.gui.unified_data_import_window - INFO - 字段映射表格创建完成: 23 行
2025-08-31 12:51:49 - src.gui.unified_data_import_window - INFO - 成功加载字段: 全部在职人员工资表 - 23 个字段
2025-08-31 12:52:14 - src.gui.unified_data_import_window - INFO - Sheet选择变化: 2 个选中
2025-08-31 12:52:14 - src.gui.unified_data_import_window - INFO - Sheet选择变化: 3 个选中
2025-08-31 12:52:14 - src.gui.unified_data_import_window - INFO - Sheet选择变化: 4 个选中
2025-08-31 12:52:24 - src.gui.unified_data_import_window - INFO - 开始打开高级配置对话框...
2025-08-31 12:52:25 - src.gui.unified_data_import_window - INFO - AdvancedConfigDialog 导入成功，开始初始化...
2025-08-31 12:52:25 - src.gui.advanced_config_dialog - INFO - 高级配置对话框开始初始化...
2025-08-31 12:52:25 - src.gui.advanced_config_dialog - INFO - 开始初始化UI...
2025-08-31 12:52:25 - src.gui.advanced_config_dialog - INFO - 开始加载配置...
2025-08-31 12:52:25 - src.gui.advanced_config_dialog - INFO - 高级配置加载成功
2025-08-31 12:52:25 - src.gui.advanced_config_dialog - INFO - 开始连接信号...
2025-08-31 12:52:25 - src.gui.advanced_config_dialog - INFO - 高级配置对话框初始化完成
2025-08-31 12:52:25 - src.gui.unified_data_import_window - INFO - AdvancedConfigDialog 初始化完成
2025-08-31 12:52:25 - src.gui.unified_data_import_window - INFO - config_changed 信号连接成功
2025-08-31 12:52:25 - src.gui.unified_data_import_window - INFO - 准备显示高级配置对话框...
2025-08-31 12:52:48 - src.gui.unified_data_import_window - INFO - 高级配置对话框关闭，返回值: 0
2025-08-31 12:52:50.735 | INFO     | src.gui.prototype.prototype_main_window:_show_unified_import_dialog:6005 | 用户取消了数据导入
2025-08-31 12:52:52.590 | INFO     | __main__:main:519 | 应用程序正常退出
