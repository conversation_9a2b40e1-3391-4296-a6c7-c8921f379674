"""
统一数据导入窗口
融合旧窗口成熟业务逻辑与新窗口现代化UI设计
"""

import sys
import logging
import json
import os
from typing import Dict, List, Optional, Any
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QSplitter,
    QWidget, QLabel, QPushButton, QComboBox, QFileDialog,
    QProgressBar, QStatusBar, QTabWidget, QTreeWidget,
    QTableWidget, QTableWidgetItem, QToolBar, QFrame, QGroupBox,
    QScrollArea, QLineEdit, QCheckBox, QRadioButton, QButtonGroup,
    QTextEdit, QSpinBox, QDoubleSpinBox, QSlider, QListWidget,
    QListWidgetItem, QMessageBox, QApplication, QSizePolicy,
    QHeaderView, QTreeWidgetItem
)
from PyQt5.QtCore import Qt, pyqtSignal, QThread, QTimer
from PyQt5.QtGui import QIcon, QFont

# 导入现有的核心业务组件
from src.modules.data_import.multi_sheet_importer import MultiSheetImporter
from src.modules.data_import.excel_importer import ExcelImporter
from src.modules.data_import.data_validator import DataValidator
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.modules.system_config.config_manager import ConfigManager
from src.modules.system_config.user_preferences import UserPreferences
from src.modules.logging.setup_logger import setup_logger


class UnifiedDataImportWindow(QDialog):
    """统一数据导入窗口主类"""
    
    # 信号定义
    import_completed = pyqtSignal(bool, str)  # 导入完成信号(成功, 消息)
    status_updated = pyqtSignal(str)  # 状态更新信号
    progress_updated = pyqtSignal(int)  # 进度更新信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 设置窗口属性
        self.setWindowTitle("统一数据导入配置")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)
        
        # 添加窗口控制按钮
        self.setWindowFlags(
            Qt.Window | 
            Qt.WindowMaximizeButtonHint | 
            Qt.WindowMinimizeButtonHint | 
            Qt.WindowCloseButtonHint
        )
        
        # 初始化日志
        self.logger = setup_logger(__name__)
        self.logger.info("初始化统一数据导入窗口")
        
        # 初始化核心组件
        self._init_core_components()
        
        # 初始化变量
        self._init_variables()
        
        # 创建UI
        self._init_ui()
        
        # 连接信号
        self._connect_signals()
        
        # 初始化响应式布局（延迟执行确保UI完全创建）
        QTimer.singleShot(50, self._initialize_responsive_layout)
        
        self.logger.info("统一数据导入窗口初始化完成")
    
    def _initialize_responsive_layout(self):
        """初始化响应式布局"""
        try:
            # 确保分割器尺寸正确设置
            self._setup_responsive_splitter()
            
            # 确保表格列宽正确设置
            if hasattr(self, 'mapping_tab') and hasattr(self.mapping_tab, 'mapping_table'):
                self.mapping_tab._setup_table_responsive_columns(self.mapping_tab.mapping_table)
            
            # 确保Sheet树列宽正确设置
            if hasattr(self, 'sheet_management_widget') and hasattr(self.sheet_management_widget, 'sheet_tree'):
                self.sheet_management_widget._setup_sheet_tree_responsive_columns(self.sheet_management_widget.sheet_tree)
            
            # 根据窗口大小优化组件间距
            self._optimize_component_spacing()
            
            self.logger.info("响应式布局初始化完成")
            
        except Exception as e:
            self.logger.warning(f"响应式布局初始化失败: {e}")
    
    def _optimize_component_spacing(self):
        """根据窗口大小优化组件间距"""
        try:
            window_width = self.width()
            window_height = self.height()
            
            # 根据窗口大小调整间距策略
            if window_width < 1000:
                # 小窗口：紧凑布局
                spacing_factor = 0.7
                margin_factor = 0.8
                self.logger.debug("应用紧凑布局（小窗口）")
            elif window_width > 1600:
                # 大窗口：宽松布局
                spacing_factor = 1.3
                margin_factor = 1.2
                self.logger.debug("应用宽松布局（大窗口）")
            else:
                # 标准窗口：默认布局
                spacing_factor = 1.0
                margin_factor = 1.0
                self.logger.debug("应用标准布局（中等窗口）")
            
            # 应用动态间距到主要布局容器
            if hasattr(self, 'main_content'):
                # 这里可以根据需要调整主要内容区域的间距
                pass
            
            # 记录当前的布局优化状态
            self._current_layout_state = {
                'window_size': (window_width, window_height),
                'spacing_factor': spacing_factor,
                'margin_factor': margin_factor
            }
            
        except Exception as e:
            self.logger.warning(f"组件间距优化失败: {e}")
    
    def resizeEvent(self, event):
        """窗口大小变化事件处理"""
        super().resizeEvent(event)
        
        # 延迟更新分割器尺寸，避免频繁调整
        if hasattr(self, '_resize_timer'):
            self._resize_timer.stop()
        else:
            from PyQt5.QtCore import QTimer
            self._resize_timer = QTimer()
            self._resize_timer.setSingleShot(True)
            self._resize_timer.timeout.connect(self._update_layout_on_resize)
        
        # 100ms延迟更新，避免拖动时频繁刷新
        self._resize_timer.start(100)
    
    def _update_layout_on_resize(self):
        """响应窗口大小变化更新布局"""
        try:
            # 更新分割器尺寸
            self._update_splitter_sizes()
            
            # 更新表格列宽
            if hasattr(self, 'mapping_tab') and hasattr(self.mapping_tab, 'mapping_table'):
                self._update_table_column_widths()
            
            # 更新Sheet树列宽
            if hasattr(self, 'sheet_management_widget') and hasattr(self.sheet_management_widget, 'sheet_tree'):
                self._update_sheet_tree_column_widths()
            
            self.logger.debug(f"布局已更新，窗口尺寸: {self.width()}x{self.height()}")
            
        except Exception as e:
            self.logger.warning(f"布局更新失败: {e}")
    
    def _update_sheet_tree_column_widths(self):
        """更新Sheet树列宽（响应式）"""
        try:
            tree = self.sheet_management_widget.sheet_tree
            if not tree or tree.columnCount() == 0:
                return
            
            # 获取左侧面板的实际宽度
            panel_width = 250  # 默认值
            if hasattr(self, 'main_splitter') and self.main_splitter.sizes():
                panel_width = self.main_splitter.sizes()[0]
            
            # 预留边距和滚动条宽度
            available_width = max(panel_width - 30, 200)
            
            # 定义各列的相对宽度比例
            column_ratios = [0.50, 0.20, 0.20, 0.10]  # Sheet名称、状态、记录数、操作
            min_widths = [80, 40, 50, 30]
            
            # 计算并设置列宽
            for col, (ratio, min_width) in enumerate(zip(column_ratios, min_widths)):
                if col < tree.columnCount():
                    calculated_width = int(available_width * ratio)
                    final_width = max(calculated_width, min_width)
                    tree.setColumnWidth(col, final_width)
            
            self.logger.debug(f"Sheet树列宽已更新，面板宽度: {panel_width}px")
            
        except Exception as e:
            self.logger.warning(f"Sheet树列宽更新失败: {e}")
    
    def _update_table_column_widths(self):
        """更新表格列宽（响应式）"""
        try:
            table = self.mapping_tab.mapping_table
            if not table or table.columnCount() == 0:
                return
            
            # 获取表格可用宽度（减去边距和滚动条宽度）
            available_width = table.viewport().width() - 20  # 预留20px边距
            
            if available_width < 400:  # 最小宽度限制
                available_width = 400
            
            # 定义各列的相对宽度比例
            column_ratios = [0.25, 0.25, 0.20, 0.15, 0.10, 0.05]  # 总和为1.0
            
            # 计算并设置列宽
            for col, ratio in enumerate(column_ratios):
                if col < table.columnCount():
                    column_width = int(available_width * ratio)
                    table.setColumnWidth(col, max(column_width, 60))  # 最小60px
            
            self.logger.debug(f"表格列宽已更新，可用宽度: {available_width}px")
            
        except Exception as e:
            self.logger.warning(f"表格列宽更新失败: {e}")
    
    def _init_core_components(self):
        """初始化核心业务组件"""
        try:
            # 导入管理器
            self.import_manager = UnifiedImportManager()
            
            # 配置管理器
            self.config_manager = ConfigManager()
            
            # 用户偏好
            self.user_preferences = UserPreferences()
            
            self.logger.info("核心组件初始化成功")
            
        except Exception as e:
            self.logger.error(f"核心组件初始化失败: {e}")
            raise
    
    def _init_variables(self):
        # 状态跟踪
        self.current_file_path = ""
        self.current_table_type = ""
        self.import_session_active = False
        self._pending_file_path = None  # 待应用的文件路径（UI初始化前保存）
        self.mapping_configs = {}
        self.import_session_active = False
        
        # 🔧 [P0修复] 加载已保存的高级配置
        self._load_saved_advanced_config()
    
    def _load_saved_advanced_config(self):
        """加载已保存的高级配置"""
        try:
            config_file = "config/advanced_settings.json"
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    saved_config = json.load(f)
                
                # 应用已保存的配置（复用现有的应用逻辑）
                self._on_advanced_config_changed(saved_config)
                self.logger.info("🔧 [P0修复] 已加载保存的高级配置")
            else:
                self.logger.info("🔧 [P0修复] 未找到已保存的高级配置，使用默认设置")
                
        except Exception as e:
            self.logger.error(f"🔧 [P0修复] 加载高级配置失败: {e}")
            # 配置加载失败不应阻止窗口正常初始化
            import traceback
            self.logger.error(f"🔧 [P0修复] 详细错误信息: {traceback.format_exc()}")
    
    def _init_ui(self):
        """初始化用户界面"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # 顶部操作栏
        self.top_toolbar = self._create_top_toolbar()
        main_layout.addWidget(self.top_toolbar)
        
        # 主内容区域
        self.main_content = self._create_main_content_area()
        main_layout.addWidget(self.main_content, 1)  # 伸展因子为1
        
        # 底部操作面板
        self.bottom_panel = self._create_bottom_panel()
        main_layout.addWidget(self.bottom_panel)
        
        self.logger.info("UI界面创建完成")
        
        # UI初始化完成后，应用待处理的配置
        self._apply_pending_configurations()
    
    def _apply_pending_configurations(self):
        """应用UI初始化完成后的待处理配置"""
        try:
            # 应用待处理的文件路径
            if hasattr(self, '_pending_file_path') and self._pending_file_path:
                file_path = self._pending_file_path
                self.logger.info(f"应用待处理的默认文件: {file_path}")
                
                # 设置当前文件路径
                self.current_file_path = file_path
                
                # 更新UI显示
                file_name = os.path.basename(file_path)
                self.file_path_label.setText(f"文件: {file_name}")
                
                # 加载Excel文件的Sheet信息
                self._load_excel_sheets()
                
                # 发送状态更新
                self.status_updated.emit(f"✅ 已自动加载默认文件: {file_name}")
                
                # 清除待处理状态
                self._pending_file_path = None
                
                self.logger.info(f"待处理配置应用完成: {file_path}")
                
        except Exception as e:
            self.logger.error(f"应用待处理配置失败: {e}")
            import traceback
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")
    
    def _create_top_toolbar(self) -> QWidget:
        """创建顶部操作栏"""
        toolbar = QFrame()
        toolbar.setFrameStyle(QFrame.StyledPanel)
        toolbar.setMinimumHeight(80)  # 增加最小高度确保内容不被遮挡
        toolbar.setMaximumHeight(100)  # 增加最大高度

        layout = QHBoxLayout(toolbar)
        layout.setContentsMargins(15, 15, 15, 15)  # 增加边距
        layout.setSpacing(20)

        # 文件选择区
        file_group = QGroupBox("文件选择")
        file_group.setMinimumHeight(60)  # 设置最小高度
        file_layout = QHBoxLayout(file_group)
        file_layout.setContentsMargins(8, 8, 8, 8)  # 增加内边距

        self.file_path_label = QLabel("未选择文件")
        self.file_path_label.setStyleSheet("color: #666; font-size: 12px; padding: 2px;")
        self.file_path_label.setWordWrap(True)  # 允许文字换行
        self.select_file_btn = QPushButton("选择Excel文件")
        self.select_file_btn.setMinimumHeight(32)  # 增加按钮高度
        self.select_file_btn.setMaximumWidth(120)  # 限制按钮宽度

        file_layout.addWidget(self.file_path_label, 1)
        file_layout.addWidget(self.select_file_btn)

        # 表类型选择区
        type_group = QGroupBox("表类型")
        type_group.setMinimumHeight(60)  # 设置最小高度
        type_layout = QHBoxLayout(type_group)
        type_layout.setContentsMargins(8, 8, 8, 8)  # 增加内边距

        self.table_type_combo = QComboBox()
        self.table_type_combo.addItems(["工资表", "异动表"])
        self.table_type_combo.setMinimumHeight(32)  # 增加下拉框高度

        type_layout.addWidget(self.table_type_combo)

        # 快速操作按钮
        action_group = QGroupBox("操作")
        action_group.setMinimumHeight(60)  # 设置最小高度
        action_layout = QHBoxLayout(action_group)
        action_layout.setContentsMargins(8, 8, 8, 8)  # 增加内边距

        self.advanced_settings_btn = QPushButton("高级设置")
        self.help_btn = QPushButton("帮助")
        self.advanced_settings_btn.setMinimumHeight(32)  # 增加按钮高度
        self.help_btn.setMinimumHeight(32)  # 增加按钮高度
        self.advanced_settings_btn.setMaximumWidth(100)  # 限制按钮宽度
        self.help_btn.setMaximumWidth(80)  # 限制按钮宽度

        # 设置工具提示
        self.select_file_btn.setToolTip("选择要导入的Excel文件")
        self.table_type_combo.setToolTip("选择数据表类型：工资表或异动表")
        self.advanced_settings_btn.setToolTip("打开系统高级配置，统一管理所有功能区域的设置选项")
        self.help_btn.setToolTip("查看使用帮助和说明")

        action_layout.addWidget(self.advanced_settings_btn)
        action_layout.addWidget(self.help_btn)

        # 添加到主布局
        layout.addWidget(file_group, 2)
        layout.addWidget(type_group, 1)
        layout.addWidget(action_group, 1)

        return toolbar
    
    def _create_main_content_area(self) -> QWidget:
        """创建主内容区域"""
        # 使用水平分割器
        self.main_splitter = QSplitter(Qt.Horizontal)
        
        # 左侧：Sheet管理面板
        self.sheet_management_widget = EnhancedSheetManagementWidget()
        self.main_splitter.addWidget(self.sheet_management_widget)
        
        # 右侧：配置详情面板
        self.config_details_widget = self._create_config_details_panel()
        self.main_splitter.addWidget(self.config_details_widget)
        
        # 设置响应式分割比例
        self._setup_responsive_splitter()
        
        return self.main_splitter
    
    def _setup_responsive_splitter(self):
        """设置响应式分割器"""
        # 获取窗口初始宽度，如果还未显示则使用默认值
        window_width = self.width() if self.width() > 100 else 1400
        
        # 计算动态分割比例
        # 左侧面板：最小250px，最大350px，占比20-25%
        left_min_width = 250
        left_max_width = 350
        left_ratio = 0.22  # 默认22%
        
        left_width = max(left_min_width, min(left_max_width, int(window_width * left_ratio)))
        right_width = window_width - left_width
        
        # 设置分割器尺寸
        self.main_splitter.setSizes([left_width, right_width])
        
        # 设置伸展因子：左侧固定，右侧可伸展
        self.main_splitter.setStretchFactor(0, 0)  
        self.main_splitter.setStretchFactor(1, 1)
        
        # 设置分割器属性
        self.main_splitter.setCollapsible(0, False)  # 左侧面板不可完全折叠
        self.main_splitter.setCollapsible(1, False)  # 右侧面板不可完全折叠
        
        self.logger.info(f"响应式分割器设置完成: 左侧={left_width}px, 右侧={right_width}px")
    
    def _update_splitter_sizes(self):
        """更新分割器尺寸（响应窗口变化）"""
        if not hasattr(self, 'main_splitter'):
            return
            
        # 重新计算并应用分割比例
        self._setup_responsive_splitter()
    
    def _create_config_details_panel(self) -> QWidget:
        """创建右侧配置详情面板"""
        # 使用选项卡容器
        tab_widget = QTabWidget()
        
        # 字段映射选项卡
        self.mapping_tab = UnifiedMappingConfigWidget()
        tab_widget.addTab(self.mapping_tab, "🔗 字段映射")
        
        # 数据处理选项卡
        self.processing_tab = DataProcessingWidget()
        tab_widget.addTab(self.processing_tab, "🧹 数据处理")
        
        # 预览验证选项卡
        self.preview_tab = PreviewValidationWidget()
        tab_widget.addTab(self.preview_tab, "👁️ 预览验证")
        
        return tab_widget
    
    def _create_bottom_panel(self) -> QWidget:
        """创建底部操作面板"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.StyledPanel)
        panel.setMaximumHeight(50)
        
        layout = QHBoxLayout(panel)
        layout.setContentsMargins(15, 5, 15, 5)
        layout.setSpacing(10)
        
        # 状态信息区
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("color: #666; font-size: 12px;")
        
        # 进度显示区
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumWidth(200)
        
        # 操作按钮组
        button_layout = QHBoxLayout()
        
        self.preview_btn = QPushButton("🔍 预览数据")
        self.save_config_btn = QPushButton("💾 保存配置")
        self.start_import_btn = QPushButton("🚀 开始导入")
        self.cancel_btn = QPushButton("❌ 取消")
        
        # 设置按钮样式和高度
        for btn in [self.preview_btn, self.save_config_btn, self.start_import_btn, self.cancel_btn]:
            btn.setMinimumHeight(30)
            btn.setMinimumWidth(100)
        
        # 重要按钮高亮
        self.start_import_btn.setStyleSheet(
            "QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }"
            "QPushButton:hover { background-color: #45a049; }"
        )
        
        button_layout.addWidget(self.preview_btn)
        button_layout.addWidget(self.save_config_btn)
        button_layout.addWidget(self.start_import_btn)
        button_layout.addWidget(self.cancel_btn)
        
        # 添加到主布局
        layout.addWidget(self.status_label, 1)
        layout.addWidget(self.progress_bar)
        layout.addLayout(button_layout)
        
        return panel
    
    def _connect_signals(self):
        """连接信号"""
        # 文件选择
        self.select_file_btn.clicked.connect(self._select_excel_file)
        
        # 表类型变化
        self.table_type_combo.currentTextChanged.connect(self._on_table_type_changed)
        
        # 顶部操作按钮
        self.advanced_settings_btn.clicked.connect(self._open_unified_advanced_settings)
        
        # 底部按钮
        self.preview_btn.clicked.connect(self._preview_data)
        self.save_config_btn.clicked.connect(self._save_configuration)
        self.start_import_btn.clicked.connect(self._start_import)
        self.cancel_btn.clicked.connect(self.reject)
        
        # 状态更新
        self.status_updated.connect(self.status_label.setText)
        self.progress_updated.connect(self.progress_bar.setValue)
        
        # Sheet管理组件信号
        self.sheet_management_widget.sheet_selection_changed.connect(self._on_sheet_selection_changed)
        self.sheet_management_widget.sheet_preview_requested.connect(self._on_sheet_preview_requested)
        self.sheet_management_widget.import_strategy_changed.connect(self._on_import_strategy_changed)
        
        # 映射配置组件信号
        self.mapping_tab.mapping_changed.connect(self._on_mapping_changed)
        self.mapping_tab.validation_completed.connect(self._on_mapping_validation_completed)
        
        self.logger.info("信号连接完成")
    
    def _select_excel_file(self):
        """选择Excel文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择Excel文件", 
            "",
            "Excel文件 (*.xlsx *.xls);;所有文件 (*)"
        )
        
        if file_path:
            self.current_file_path = file_path
            self.file_path_label.setText(f"文件: {file_path.split('/')[-1]}")
            self._load_excel_sheets()
            self.logger.info(f"选择文件: {file_path}")
    
    def _on_table_type_changed(self, table_type: str):
        """表类型变化处理"""
        self.current_table_type = table_type
        self.logger.info(f"用户选择表类型: {table_type}")
        
        # 根据用户选择的表类型更新界面提示和行为
        if table_type == "💰 工资表":
            self.status_updated.emit("已选择工资表类型 - 将使用标准工资表模板和字段映射")
            
            # 工资表使用专用模板，字段相对固定
            tooltip_text = "工资表：使用标准化字段映射，支持专用模板自动识别（离休、退休、在职、A岗等）"
            
        elif table_type == "🔄 异动表":
            self.status_updated.emit("已选择异动表类型 - 支持灵活的字段配置和自定义映射")
            
            # 异动表更灵活，支持完全自定义
            tooltip_text = "异动表：支持灵活的字段配置，可完全自定义字段映射，内容结构灵活多样"
            
        else:
            self.status_updated.emit(f"已选择表类型: {table_type}")
            tooltip_text = f"当前选择的表类型: {table_type}"
        
        # 更新UI提示
        self.table_type_combo.setToolTip(tooltip_text)
        
        # 根据表类型重新初始化导入会话
        if self.current_file_path:
            self._initialize_import_session()
            
            # 如果已有Sheet信息，重新加载以应用类型特定的处理
            if hasattr(self, 'sheet_management_widget') and self.sheet_management_widget.sheet_info:
                self._update_sheet_display_for_table_type()
    
    def _update_sheet_display_for_table_type(self):
        """根据表类型更新Sheet显示"""
        try:
            if self.current_table_type == "💰 工资表":
                # 工资表：尝试智能识别专用Sheet类型
                sheet_info = self.sheet_management_widget.sheet_info
                for sheet in sheet_info:
                    sheet_name = sheet['name'].lower()
                    
                    # 标记专用工资表类型
                    if '离休' in sheet_name:
                        sheet['detected_type'] = 'retired_employees'
                        sheet['tooltip'] = '检测到：离休人员工资表'
                    elif '退休' in sheet_name:
                        sheet['detected_type'] = 'pension_employees'
                        sheet['tooltip'] = '检测到：退休人员工资表'
                    elif '在职' in sheet_name or '全部' in sheet_name:
                        sheet['detected_type'] = 'active_employees'
                        sheet['tooltip'] = '检测到：在职人员工资表'
                    elif 'a岗' in sheet_name or 'A岗' in sheet_name:
                        sheet['detected_type'] = 'a_grade_employees'
                        sheet['tooltip'] = '检测到：A岗职工工资表'
                    else:
                        sheet['detected_type'] = 'general_salary'
                        sheet['tooltip'] = '工资表（未识别具体类型）'
                
                self.status_updated.emit("已为工资表标记专用类型，将自动应用相应模板")
                
            elif self.current_table_type == "🔄 异动表":
                # 异动表：标记为灵活类型
                sheet_info = self.sheet_management_widget.sheet_info
                for sheet in sheet_info:
                    sheet['detected_type'] = 'change_data'
                    sheet['tooltip'] = '异动表：支持灵活字段配置'
                
                self.status_updated.emit("已标记为异动表，支持完全自定义的字段映射")
            
            # 刷新Sheet管理组件显示
            self.sheet_management_widget.load_sheets(self.sheet_management_widget.sheet_info)
            
        except Exception as e:
            self.logger.warning(f"更新Sheet显示失败: {e}")
    
    def _load_excel_sheets(self):
        """加载Excel文件的Sheet列表"""
        if not self.current_file_path:
            return
        
        try:
            self.status_updated.emit("正在分析Excel文件...")
            
            # 使用导入管理器分析文件
            sheet_info = self.import_manager.analyze_excel_file(self.current_file_path)
            
            # 更新Sheet管理组件
            self.sheet_management_widget.load_sheets(sheet_info)
            
            self.status_updated.emit(f"已加载 {len(sheet_info)} 个工作表")
            self.logger.info(f"成功加载 {len(sheet_info)} 个工作表")
            
        except Exception as e:
            error_msg = f"加载Excel文件失败: {e}"
            self.status_updated.emit(error_msg)
            self.logger.error(error_msg)
    
    def _initialize_import_session(self):
        """初始化导入会话"""
        if not self.current_file_path or not self.current_table_type:
            return
        
        try:
            self.status_updated.emit("正在初始化导入会话...")
            
            # 使用导入管理器初始化会话
            self.import_manager.initialize_import_session(
                self.current_file_path, 
                self.current_table_type
            )
            
            self.import_session_active = True
            self.status_updated.emit("导入会话已初始化")
            self.logger.info("导入会话初始化成功")
            
        except Exception as e:
            error_msg = f"初始化导入会话失败: {e}"
            self.status_updated.emit(error_msg)
            self.logger.error(error_msg)
    
    def _preview_data(self):
        """预览数据"""
        self.logger.info("开始预览数据")
        
        if not self.current_file_path:
            self.status_updated.emit("请先选择Excel文件")
            return
        
        try:
            # 获取选中的Sheet
            selected_sheets = self.sheet_management_widget.get_selected_sheets()
            if not selected_sheets:
                self.status_updated.emit("请先选择要预览的工作表")
                return
            
            # 预览第一个选中的Sheet
            first_sheet = selected_sheets[0]
            sheet_name = first_sheet['name']
            
            self.status_updated.emit(f"正在预览工作表: {sheet_name}")
            
            # 使用ExcelImporter预览数据（前10行）
            preview_data = self.import_manager.excel_importer.import_data(
                self.current_file_path, 
                sheet_name=sheet_name, 
                max_rows=10
            )
            
            if preview_data is not None and len(preview_data) > 0:
                # 转换DataFrame为字典列表格式
                data_dicts = preview_data.to_dict('records')
                
                # 显示预览数据在预览选项卡
                self._show_preview_data(sheet_name, data_dicts)
                self.status_updated.emit(f"预览完成: {len(data_dicts)} 行数据")
                
                # 切换到预览选项卡
                config_details = self.config_details_widget
                if hasattr(config_details, 'setCurrentIndex'):
                    config_details.setCurrentIndex(2)  # 预览验证选项卡
            else:
                self.status_updated.emit(f"工作表 '{sheet_name}' 无数据可预览")
                
        except Exception as e:
            error_msg = f"预览数据失败: {e}"
            self.status_updated.emit(error_msg)
            self.logger.error(error_msg)
    
    def _save_configuration(self):
        """保存配置"""
        self.logger.info("保存配置")
        
        if not self.current_file_path:
            self.status_updated.emit("请先选择Excel文件")
            return
        
        try:
            # 获取当前映射配置
            mapping_config = self.mapping_tab.get_mapping_config()
            if not mapping_config:
                self.status_updated.emit("没有可保存的映射配置")
                return
            
            # 获取选中的Sheet配置
            selected_sheets = self.sheet_management_widget.get_selected_sheets()
            import_strategy = self.sheet_management_widget.get_import_strategy()
            
            # 构建完整配置
            full_config = {
                'file_path': self.current_file_path,
                'table_type': self.current_table_type,
                'selected_sheets': selected_sheets,
                'import_strategy': import_strategy,
                'mapping_config': mapping_config,
                'session_info': {
                    'created_time': self.import_manager.current_session.get('created_time') if self.import_manager.current_session else None,
                    'file_name': self.current_file_path.split('/')[-1] if self.current_file_path else None
                }
            }
            
            # 使用配置管理器保存配置
            config_name = f"import_config_{self.current_table_type}_{len(mapping_config)}fields"
            if self.config_manager.save_configuration(config_name, full_config):
                self.status_updated.emit(f"配置已保存: {config_name}")
                self.logger.info(f"配置保存成功: {config_name}")
                
                # 同时保存为模板（如果映射配置足够完整）
                if len(mapping_config) >= 3:  # 至少3个字段才保存为模板
                    try:
                        template_data = {
                            'name': f"{self.current_table_type}_自动保存模板",
                            'description': f"从{self.current_file_path.split('/')[-1]}自动生成",
                            'table_type': self.current_table_type,
                            'mapping_config': mapping_config,
                            'field_count': len(mapping_config),
                            'auto_generated': True
                        }
                        
                        if self.mapping_tab.template_manager.save_enhanced_template(template_data):
                            self.status_updated.emit(f"配置已保存，并自动创建模板")
                    except Exception as e:
                        self.logger.warning(f"模板保存失败，但配置保存成功: {e}")
            else:
                self.status_updated.emit("配置保存失败")
                
        except Exception as e:
            error_msg = f"保存配置失败: {e}"
            self.status_updated.emit(error_msg)
            self.logger.error(error_msg)
    
    def _start_import(self):
        """开始导入"""
        self.logger.info("开始数据导入")
        
        if not self.current_file_path:
            self.status_updated.emit("请先选择Excel文件")
            return
        
        if not self.current_table_type:
            self.status_updated.emit("请先选择表类型")
            return
        
        try:
            # 获取选中的Sheet
            selected_sheets = self.sheet_management_widget.get_selected_sheets()
            if not selected_sheets:
                self.status_updated.emit("请先选择要导入的工作表")
                return
            
            # 确认映射配置是否完整
            mapping_config = self.mapping_tab.get_mapping_config()
            if not mapping_config:
                self.status_updated.emit("请先配置字段映射")
                return
            
            # 显示进度条
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            self.status_updated.emit("正在准备导入...")
            
            # 获取当前年月（从文件名推断或使用当前日期）
            import datetime
            current_date = datetime.datetime.now()
            year = current_date.year
            month = current_date.month
            
            # 尝试从文件名推断年月
            file_name = self.current_file_path.split('/')[-1].lower()
            if '2025' in file_name:
                year = 2025
            if '5月' in file_name or '05' in file_name:
                month = 5
            elif '8月' in file_name or '08' in file_name:
                month = 8
            
            # 确定target_path（这是用户决策的关键）
            if self.current_table_type == "🔄 异动表":
                target_path = "异动人员表"
            else:
                target_path = "工资表"
            
            self.progress_bar.setValue(20)
            self.status_updated.emit("正在执行数据导入...")
            
            # 调用MultiSheetImporter执行导入
            result = self.import_manager.multi_sheet_importer.import_excel_file(
                file_path=self.current_file_path,
                year=year,
                month=month,
                target_table=None,  # 使用默认命名规则
                target_path=target_path
            )
            
            self.progress_bar.setValue(90)
            
            # 处理导入结果
            if result and result.get('success', False):
                imported_tables = result.get('imported_tables', [])
                total_records = result.get('total_records', 0)
                
                success_msg = f"导入完成！共导入 {total_records} 条记录到 {len(imported_tables)} 个表"
                self.status_updated.emit(success_msg)
                self.logger.info(f"导入成功: {success_msg}")
                
                # 发送导入完成信号
                self.import_completed.emit(True, success_msg)
                
                # 自动保存配置
                try:
                    self._save_configuration()
                except Exception as e:
                    self.logger.warning(f"自动保存配置失败: {e}")
                
            else:
                error_msg = result.get('error', '导入失败，未知错误') if result else '导入失败，未知错误'
                self.status_updated.emit(f"导入失败: {error_msg}")
                self.logger.error(f"导入失败: {error_msg}")
                
                # 发送导入失败信号
                self.import_completed.emit(False, error_msg)
            
            self.progress_bar.setValue(100)
            
        except Exception as e:
            error_msg = f"导入过程中发生错误: {e}"
            self.status_updated.emit(error_msg)
            self.logger.error(error_msg)
            
            # 发送导入失败信号
            self.import_completed.emit(False, error_msg)
            
        finally:
            # 隐藏进度条
            self.progress_bar.setVisible(False)
    
    def _on_sheet_selection_changed(self, selected_sheets: List[Dict]):
        """Sheet选择变化处理"""
        self.logger.info(f"Sheet选择变化: {len(selected_sheets)} 个选中")
        
        if not selected_sheets:
            self.status_updated.emit("未选择工作表")
            return
        
        # 如果只选择了一个Sheet，自动加载其字段到映射配置
        if len(selected_sheets) == 1:
            sheet = selected_sheets[0]
            self._load_sheet_headers(sheet['name'])
        else:
            # 多个Sheet选择，可能需要分析字段兼容性
            self.status_updated.emit(f"已选择 {len(selected_sheets)} 个工作表")
    
    def _on_sheet_preview_requested(self, sheet_name: str):
        """Sheet预览请求处理"""
        try:
            self.status_updated.emit(f"🔍 正在预览工作表: {sheet_name}")
            self.logger.info(f"开始预览Sheet: {sheet_name}")

            # 使用ExcelImporter预览数据 - 修复：处理DataFrame返回值
            df = self.import_manager.excel_importer.import_data(
                self.current_file_path, sheet_name, max_rows=10
            )

            if df is not None and not df.empty:
                # 将DataFrame转换为字典列表用于显示
                preview_data = df.to_dict('records')

                self.logger.info(f"成功读取预览数据: {len(preview_data)} 行, {len(df.columns)} 列")

                # 显示预览数据在预览选项卡
                self._show_preview_data(sheet_name, preview_data)
                self.status_updated.emit(f"✅ 预览加载完成: {len(preview_data)} 行数据")
            else:
                self.status_updated.emit(f"⚠️ 工作表 '{sheet_name}' 无数据")
                self.logger.warning(f"工作表 '{sheet_name}' 数据为空")

        except Exception as e:
            error_msg = f"❌ 预览失败: {e}"
            self.status_updated.emit(error_msg)
            self.logger.error(f"预览Sheet失败: {sheet_name} - {e}")
            import traceback
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")
    
    def _on_import_strategy_changed(self, strategy: str):
        """导入策略变化处理"""
        self.logger.info(f"导入策略变更: {strategy}")
        
        if strategy == "merge_to_single_table":
            self.status_updated.emit("策略: 合并到单表")
        else:
            self.status_updated.emit("策略: 分别创建表")
    
    def _on_mapping_changed(self):
        """映射配置变化处理"""
        self.logger.info("映射配置已变化")
        # 可以触发自动验证
    
    def _on_mapping_validation_completed(self, is_valid: bool):
        """映射验证完成处理"""
        if is_valid:
            self.start_import_btn.setEnabled(True)
            self.start_import_btn.setStyleSheet(
                "QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }"
                "QPushButton:hover { background-color: #45a049; }"
            )
        else:
            self.start_import_btn.setEnabled(False)
            self.start_import_btn.setStyleSheet(
                "QPushButton { background-color: #ccc; color: #666; }"
            )
    
    def _load_sheet_headers(self, sheet_name: str):
        """加载Sheet的字段头到映射配置"""
        try:
            self.status_updated.emit(f"正在加载字段: {sheet_name}")
            self.logger.info(f"开始加载Sheet字段: {sheet_name}")

            # 获取Sheet的字段头 - 修复：使用正确的DataFrame方式
            df = self.import_manager.excel_importer.import_data(
                self.current_file_path, sheet_name, max_rows=1
            )

            if df is not None and not df.empty:
                # DataFrame的列名就是字段头
                headers = list(df.columns)

                self.logger.info(f"成功读取字段头: {headers}")

                # 加载到映射配置组件
                self.mapping_tab.load_excel_headers(headers, self.current_table_type)

                self.status_updated.emit(f"✅ 已加载 {len(headers)} 个字段")
                self.logger.info(f"成功加载字段: {sheet_name} - {len(headers)} 个字段")
            else:
                self.status_updated.emit(f"⚠️ 工作表 '{sheet_name}' 无字段信息")
                self.logger.warning(f"工作表 '{sheet_name}' 数据为空或无法读取")

        except Exception as e:
            error_msg = f"❌ 加载字段失败: {e}"
            self.status_updated.emit(error_msg)
            self.logger.error(f"加载Sheet字段失败: {sheet_name} - {e}")
            import traceback
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")
    
    def _show_preview_data(self, sheet_name: str, data: List[Dict]):
        """在预览选项卡显示数据"""
        try:
            # 切换到预览选项卡
            config_details = self.config_details_widget
            if hasattr(config_details, 'setCurrentIndex'):
                config_details.setCurrentIndex(2)  # 预览验证选项卡
            
            # 更新预览组件
            self.preview_tab.show_preview_data(sheet_name, data)
            
        except Exception as e:
            self.logger.error(f"显示预览数据失败: {e}")
    
    def _open_unified_advanced_settings(self):
        """打开统一高级配置对话框"""
        try:
            self.logger.info("开始打开高级配置对话框...")
            from src.gui.advanced_config_dialog import AdvancedConfigDialog
            
            self.logger.info("AdvancedConfigDialog 导入成功，开始初始化...")
            dialog = AdvancedConfigDialog(self)
            self.logger.info("AdvancedConfigDialog 初始化完成")
            
            if hasattr(dialog, 'config_changed'):
                dialog.config_changed.connect(self._on_advanced_config_changed)
                self.logger.info("config_changed 信号连接成功")
            
            self.logger.info("准备显示高级配置对话框...")
            result = dialog.exec_()
            self.logger.info(f"高级配置对话框关闭，返回值: {result}")
            
            if result == dialog.Accepted:
                self.status_updated.emit("高级配置已更新")
                self.logger.info("统一高级配置已更新")
            
        except Exception as e:
            import traceback
            error_msg = f"打开高级配置失败: {e}"
            detailed_error = f"详细错误信息: {traceback.format_exc()}"
            self.status_updated.emit(error_msg)
            self.logger.error(error_msg)
            self.logger.error(detailed_error)
    
    def _on_advanced_config_changed(self, config):
        """处理高级配置变化"""
        try:
            self.logger.info(f"接收到高级配置变化: {config}")
            
            # 文件导入配置 - 优先处理
            file_config = config.get('file_import', {})
            if file_config:
                self._apply_file_import_config(file_config)
                
            # 字段映射配置 - 其次处理
            mapping_config = config.get('field_mapping', {})
            if mapping_config:
                self._apply_field_mapping_config(mapping_config)
            
            # 智能推荐配置
            smart_config = config.get('smart_recommendations', {})
            if smart_config:
                self._apply_smart_recommendations_config(smart_config)
            
            # 数据处理配置
            data_config = config.get('data_processing', {})
            if data_config:
                self._apply_data_processing_config(data_config)
            
            # 界面个性化配置
            ui_config = config.get('ui_customization', {})
            if ui_config:
                self._apply_ui_customization_config(ui_config)
            
            # 性能优化配置
            perf_config = config.get('performance', {})
            if perf_config:
                self._apply_performance_config(perf_config)
            
            # 保存配置
            self.advanced_config = config
            self.status_updated.emit("✅ 高级配置已应用")
            self.logger.info(f"所有高级配置域已成功应用: {list(config.keys())}")
            
        except Exception as e:
            error_msg = f"处理高级配置变化失败: {e}"
            self.status_updated.emit(error_msg)
            self.logger.error(error_msg)
    
    def _apply_file_import_config(self, config: Dict[str, Any]):
        """应用文件导入配置"""
        try:
            # 设置默认文件路径
            default_path = config.get('default_import_path', '').strip()
            if default_path:
                import os
                # 标准化路径分隔符
                default_path = os.path.normpath(default_path)
                
                if os.path.isfile(default_path):
                    # 如果是具体文件，直接加载
                    self.logger.info(f"应用默认导入文件: {default_path}")
                    self.current_file_path = default_path
                    
                    # 检查UI是否已初始化，避免在UI创建前访问组件
                    if hasattr(self, 'file_path_label') and self.file_path_label is not None:
                        # 获取文件名（兼容Windows和Unix路径）
                        file_name = os.path.basename(default_path)
                        self.file_path_label.setText(f"文件: {file_name}")
                        
                        # 加载Excel文件的Sheet信息
                        self._load_excel_sheets()
                    else:
                        # UI未初始化时，仅保存路径，稍后在UI初始化完成后应用
                        self.logger.info(f"UI未初始化，保存默认路径供后续应用: {default_path}")
                        # 保存待应用的文件路径
                        self._pending_file_path = default_path
                    
                elif os.path.isdir(default_path):
                    # 如果是目录，设置为默认目录
                    self.logger.info(f"设置默认导入目录: {default_path}")
                    # 这里可以存储默认目录偏好
                    pass
                else:
                    self.logger.warning(f"默认文件路径不存在: {default_path}")
                    self.status_updated.emit(f"⚠️ 默认文件不存在: {os.path.basename(default_path)}")
            
            # 设置支持的文件格式
            supported_formats = config.get('supported_formats', ['xlsx', 'xls', 'csv'])
            if hasattr(self, 'file_dialog_filters'):
                # 更新文件对话框过滤器
                pass
            
            # 设置文件大小限制
            max_file_size = config.get('max_file_size_mb', 100)
            if hasattr(self.import_manager, 'set_max_file_size'):
                self.import_manager.set_max_file_size(max_file_size)
            
            # 设置编码检测
            auto_detect_encoding = config.get('auto_detect_encoding', True)
            if hasattr(self.import_manager, 'enable_encoding_detection'):
                self.import_manager.enable_encoding_detection(auto_detect_encoding)
            
            # 设置Sheet自动选择策略
            sheet_selection_strategy = config.get('sheet_selection_strategy', 'all')
            if hasattr(self, 'sheet_management_widget'):
                if hasattr(self.sheet_management_widget, 'set_selection_strategy'):
                    self.sheet_management_widget.set_selection_strategy(sheet_selection_strategy)
            
            # 应用Excel表结构配置
            excel_structure = {
                'data_start_row': config.get('data_start_row', 2),
                'header_row': config.get('header_row', 1),
                'skip_rows': config.get('skip_rows', 0),
                'skip_footer_rows': config.get('skip_footer_rows', 0),
                'auto_detect_header': config.get('auto_detect_header', True),
                'ignore_empty_rows': config.get('ignore_empty_rows', True)
            }
            
            # 应用到导入管理器
            if hasattr(self.import_manager, 'excel_importer'):
                if hasattr(self.import_manager.excel_importer, 'set_structure_config'):
                    self.import_manager.excel_importer.set_structure_config(excel_structure)
            
            # 保存结构配置供后续使用
            self.excel_structure_config = excel_structure
            
            self.logger.info(f"文件导入配置已应用，包括Excel表结构设置: {excel_structure}")
            
        except Exception as e:
            self.logger.error(f"应用文件导入配置失败: {e}")
    
    def _apply_field_mapping_config(self, config: Dict[str, Any]):
        """应用字段映射配置"""
        try:
            # 设置映射算法类型
            mapping_algorithm = config.get('mapping_algorithm', 'fuzzy_match')
            if hasattr(self.import_manager, 'mapping_engine'):
                engine = self.import_manager.mapping_engine
                if hasattr(engine, 'set_algorithm'):
                    engine.set_algorithm(mapping_algorithm)
            
            # 设置映射相似度阈值
            similarity_threshold = config.get('similarity_threshold', 0.8)
            if hasattr(self.import_manager, 'mapping_engine'):
                engine = self.import_manager.mapping_engine
                if hasattr(engine, 'set_similarity_threshold'):
                    engine.set_similarity_threshold(similarity_threshold)
            
            # 设置自动映射开关
            auto_mapping_enabled = config.get('auto_mapping_enabled', True)
            if hasattr(self, 'mapping_tab'):
                if hasattr(self.mapping_tab, 'set_auto_mapping'):
                    self.mapping_tab.set_auto_mapping(auto_mapping_enabled)
            
            # 设置必填字段检查
            required_field_check = config.get('required_field_check', True)
            if hasattr(self, 'mapping_tab'):
                if hasattr(self.mapping_tab, 'enable_required_check'):
                    self.mapping_tab.enable_required_check(required_field_check)
            
            # 设置字段类型验证
            field_type_validation = config.get('field_type_validation', True)
            if hasattr(self.import_manager, 'data_validator'):
                validator = self.import_manager.data_validator
                if hasattr(validator, 'enable_type_validation'):
                    validator.enable_type_validation(field_type_validation)
            
            # 设置映射历史保存
            save_mapping_history = config.get('save_mapping_history', True)
            if hasattr(self, 'mapping_tab'):
                if hasattr(self.mapping_tab, 'enable_history_save'):
                    self.mapping_tab.enable_history_save(save_mapping_history)
            
            self.logger.info("字段映射配置已应用")
            
        except Exception as e:
            self.logger.error(f"应用字段映射配置失败: {e}")
    
    def _apply_smart_recommendations_config(self, config: Dict[str, Any]):
        """应用智能推荐配置"""
        try:
            # 配置智能映射引擎（如果存在）
            if hasattr(self.import_manager, 'mapping_engine'):
                engine = self.import_manager.mapping_engine
                
                # 设置置信度阈值
                threshold = config.get('confidence_threshold', 70) / 100.0
                if hasattr(engine, 'set_confidence_threshold'):
                    engine.set_confidence_threshold(threshold)
                
                # 启用/禁用历史学习
                if hasattr(engine, 'enable_history_learning'):
                    engine.enable_history_learning(config.get('enable_history_learning', True))
                
                # 启用/禁用语义分析
                if hasattr(engine, 'enable_semantic_analysis'):
                    engine.enable_semantic_analysis(config.get('enable_semantic_analysis', True))
            
            # 应用到映射配置组件
            if hasattr(self, 'mapping_tab'):
                # 设置自动应用高置信度推荐
                auto_apply = config.get('auto_apply_high_confidence', False)
                if hasattr(self.mapping_tab, 'set_auto_apply_high_confidence'):
                    self.mapping_tab.set_auto_apply_high_confidence(auto_apply)
                
                # 设置置信度指示器显示
                if hasattr(self.mapping_tab, 'toggle_confidence_indicators'):
                    show_confidence = config.get('show_confidence_indicators', True)
                    self.mapping_tab.toggle_confidence_indicators(show_confidence)
            
            self.logger.info("智能推荐配置已应用")
            
        except Exception as e:
            self.logger.error(f"应用智能推荐配置失败: {e}")
    
    def _apply_data_processing_config(self, config: Dict[str, Any]):
        """应用数据处理配置"""
        try:
            # 应用到导入管理器
            if hasattr(self.import_manager, 'data_validator'):
                validator = self.import_manager.data_validator
                
                # 设置严格验证模式
                if hasattr(validator, 'set_strict_mode'):
                    validator.set_strict_mode(config.get('strict_validation', False))
                
                # 设置空值处理策略
                null_strategy = config.get('null_value_strategy', 0)
                strategies = ['keep', 'default', 'skip', 'prompt']
                if hasattr(validator, 'set_null_strategy'):
                    validator.set_null_strategy(strategies[null_strategy])
                
                # 设置数据类型自动转换
                if hasattr(validator, 'enable_auto_conversion'):
                    validator.enable_auto_conversion(config.get('auto_type_conversion', True))
            
            # 应用到多表导入器
            if hasattr(self.import_manager, 'multi_sheet_importer'):
                importer = self.import_manager.multi_sheet_importer
                
                # 设置批量大小
                batch_size = config.get('batch_size', 1000)
                if hasattr(importer, 'set_batch_size'):
                    importer.set_batch_size(batch_size)
                
                # 设置错误容忍度
                tolerance = config.get('error_tolerance', 10) / 100.0
                if hasattr(importer, 'set_error_tolerance'):
                    importer.set_error_tolerance(tolerance)
            
            self.logger.info("数据处理配置已应用")
            
        except Exception as e:
            self.logger.error(f"应用数据处理配置失败: {e}")
    
    def _apply_ui_customization_config(self, config: Dict[str, Any]):
        """应用界面个性化配置"""
        try:
            # 设置表格显示行数限制
            row_limit = config.get('table_row_limit', 200)
            if hasattr(self, 'preview_tab'):
                if hasattr(self.preview_tab, 'set_row_limit'):
                    self.preview_tab.set_row_limit(row_limit)
            
            # 设置详细日志显示
            show_detailed = config.get('show_detailed_logs', False)
            if hasattr(self.logger, 'setLevel'):
                import logging
                self.logger.setLevel(logging.DEBUG if show_detailed else logging.INFO)
            
            # 设置置信度指示器显示
            show_confidence = config.get('show_confidence_indicators', True)
            if hasattr(self, 'mapping_tab'):
                if hasattr(self.mapping_tab, 'toggle_confidence_indicators'):
                    self.mapping_tab.toggle_confidence_indicators(show_confidence)
            
            # 设置自动保存间隔
            auto_save_interval = config.get('auto_save_interval', 5)
            if auto_save_interval > 0:
                # 启动自动保存计时器（如果需要的话）
                pass
            
            # 设置确认对话框显示
            self.show_confirmations = config.get('show_confirmation_dialogs', True)
            
            self.logger.info("界面个性化配置已应用")
            
        except Exception as e:
            self.logger.error(f"应用界面个性化配置失败: {e}")
    
    def _apply_performance_config(self, config: Dict[str, Any]):
        """应用性能优化配置"""
        try:
            # 设置最大内存使用限制
            max_memory = config.get('max_memory_usage', 2048)
            # 这里可以设置内存监控和限制机制
            
            # 启用/禁用缓存
            enable_cache = config.get('enable_caching', True)
            if hasattr(self.import_manager, 'enable_caching'):
                self.import_manager.enable_caching(enable_cache)
            
            # 设置预加载数据
            preload = config.get('preload_data', False)
            if hasattr(self.import_manager, 'set_preload_data'):
                self.import_manager.set_preload_data(preload)
            
            # 设置处理线程数
            thread_count = config.get('thread_count', 4)
            if hasattr(self.import_manager, 'set_thread_count'):
                self.import_manager.set_thread_count(thread_count)
            
            # 启用/禁用异步处理
            enable_async = config.get('enable_async_processing', True)
            if hasattr(self.import_manager, 'enable_async_processing'):
                self.import_manager.enable_async_processing(enable_async)
            
            # 设置进度更新频率
            update_freq = config.get('progress_update_frequency', 100)
            if hasattr(self, 'progress_bar'):
                # 可以设置进度条更新频率
                pass
            
            self.logger.info("性能优化配置已应用")
            
        except Exception as e:
            self.logger.error(f"应用性能优化配置失败: {e}")


class UnifiedImportManager:
    """统一导入管理器 - 核心业务协调"""
    
    def __init__(self):
        """初始化导入管理器"""
        self.logger = setup_logger(__name__)
        
        # 初始化原有业务组件
        self.excel_importer = ExcelImporter()
        self.data_validator = DataValidator()
        self.table_manager = DynamicTableManager()
        self.multi_sheet_importer = MultiSheetImporter(self.table_manager)
        
        # 初始化新核心组件
        from src.gui.core import SmartMappingEngine, TemplateManager, ValidationEngine
        self.mapping_engine = SmartMappingEngine()
        self.template_manager = TemplateManager()
        self.validation_engine = ValidationEngine()
        
        # 导入会话状态
        self.current_session = None
        
        self.logger.info("统一导入管理器初始化完成")
    
    def analyze_excel_file(self, file_path: str) -> List[Dict]:
        """分析Excel文件并返回Sheet信息"""
        try:
            # 使用ExcelImporter获取Sheet信息
            sheet_names = self.excel_importer.get_sheet_names(file_path)
            
            sheet_info = []
            for sheet_name in sheet_names:
                # 获取每个Sheet的基本信息
                try:
                    data = self.excel_importer.import_data(file_path, sheet_name, max_rows=5)
                    row_count = len(data) if data else 0
                    
                    sheet_info.append({
                        'name': sheet_name,
                        'row_count': row_count,
                        'enabled': True,
                        'status': 'ready'
                    })
                except Exception as e:
                    sheet_info.append({
                        'name': sheet_name,
                        'row_count': 0,
                        'enabled': False,
                        'status': f'error: {e}'
                    })
            
            return sheet_info
            
        except Exception as e:
            self.logger.error(f"分析Excel文件失败: {e}")
            raise
    
    def initialize_import_session(self, file_path: str, table_type: str):
        """初始化导入会话"""
        try:
            self.current_session = {
                'file_path': file_path,
                'table_type': table_type,
                'sheets': self.analyze_excel_file(file_path),
                'mapping_config': {},
                'processing_config': {},
                'created_time': self.logger.handlers[0].formatter.formatTime(
                    self.logger.makeRecord('', 0, '', 0, '', (), None)
                ) if self.logger.handlers else None
            }
            
            self.logger.info(f"导入会话已初始化: {table_type} - {file_path}")
            
        except Exception as e:
            self.logger.error(f"初始化导入会话失败: {e}")
            raise


class EnhancedSheetManagementWidget(QWidget):
    """增强的Sheet管理组件"""
    
    # 信号定义
    sheet_selection_changed = pyqtSignal(list)  # Sheet选择变化信号
    sheet_preview_requested = pyqtSignal(str)   # Sheet预览请求信号
    import_strategy_changed = pyqtSignal(str)   # 导入策略变化信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = setup_logger(__name__)
        
        # 初始化数据
        self.sheet_info = []
        self.current_strategy = "merge_to_single_table"
        
        self._init_ui()
        self._connect_signals()
    
    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 工具栏
        toolbar = self._create_toolbar()
        layout.addWidget(toolbar)
        
        # Sheet列表
        self.sheet_tree = self._create_sheet_tree()
        layout.addWidget(self.sheet_tree, 1)
        
        # 导入策略
        strategy_group = self._create_strategy_group()
        layout.addWidget(strategy_group)
        
        # 状态信息
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("color: #666; font-size: 12px;")
        layout.addWidget(self.status_label)
    
    def _create_toolbar(self) -> QWidget:
        """创建工具栏"""
        toolbar = QFrame()
        toolbar.setFrameStyle(QFrame.StyledPanel)
        toolbar.setMaximumHeight(45)  # 恢复单行高度

        layout = QHBoxLayout(toolbar)
        layout.setContentsMargins(10, 5, 10, 5)
        layout.setSpacing(8)

        # 所有按钮一行显示
        self.select_all_btn = QPushButton("全选")
        self.deselect_all_btn = QPushButton("全不选")
        self.refresh_btn = QPushButton("刷新")
        self.preview_btn = QPushButton("预览")
        self.analyze_btn = QPushButton("分析")

        # 设置按钮样式
        for btn in [self.select_all_btn, self.deselect_all_btn,
                   self.refresh_btn, self.preview_btn, self.analyze_btn]:
            btn.setMinimumHeight(32)
            btn.setMinimumWidth(70)

        # 设置按钮工具提示
        self.select_all_btn.setToolTip("选择所有工作表进行导入")
        self.deselect_all_btn.setToolTip("取消选择所有工作表")
        self.refresh_btn.setToolTip("重新扫描Excel文件中的工作表")
        self.preview_btn.setToolTip("预览选中工作表的数据内容")
        self.analyze_btn.setToolTip("分析工作表结构和数据质量")

        layout.addWidget(self.select_all_btn)
        layout.addWidget(self.deselect_all_btn)
        layout.addWidget(self.refresh_btn)
        layout.addWidget(self.preview_btn)
        layout.addWidget(self.analyze_btn)
        layout.addStretch()

        return toolbar
    
    def _create_sheet_tree(self) -> QTreeWidget:
        """创建Sheet列表树"""
        tree = QTreeWidget()
        tree.setHeaderLabels(["Sheet名称", "状态", "记录数", "操作"])
        
        # 设置响应式列宽
        self._setup_sheet_tree_responsive_columns(tree)
        
        # 设置属性
        tree.setAlternatingRowColors(True)
        tree.setRootIsDecorated(False)
        tree.setSelectionMode(QTreeWidget.ExtendedSelection)
        
        # 设置表头自适应策略
        header = tree.header()
        header.setStretchLastSection(False)  # 最后一列不自动拉伸
        header.setSectionResizeMode(header.Interactive)  # 允许用户调整列宽
        
        return tree
    
    def _setup_sheet_tree_responsive_columns(self, tree: QTreeWidget):
        """设置Sheet树响应式列宽"""
        try:
            # 获取左侧面板的宽度（Sheet管理面板）
            panel_width = 250  # 默认左侧面板宽度
            if hasattr(self, 'main_splitter') and self.main_splitter.sizes():
                panel_width = self.main_splitter.sizes()[0]
            
            # 预留边距和滚动条宽度
            available_width = max(panel_width - 30, 200)  # 最小200px
            
            # 定义各列的相对宽度比例和最小宽度
            column_configs = [
                {'ratio': 0.50, 'min_width': 80},   # Sheet名称 - 50%
                {'ratio': 0.20, 'min_width': 40},   # 状态 - 20%
                {'ratio': 0.20, 'min_width': 50},   # 记录数 - 20%
                {'ratio': 0.10, 'min_width': 30}    # 操作 - 10%
            ]
            
            # 计算并设置列宽
            for col, config in enumerate(column_configs):
                calculated_width = int(available_width * config['ratio'])
                final_width = max(calculated_width, config['min_width'])
                tree.setColumnWidth(col, final_width)
            
            self.logger.debug(f"Sheet树响应式列宽设置完成: 面板宽度={panel_width}px, 可用宽度={available_width}px")
            
        except Exception as e:
            # 如果响应式设置失败，使用默认固定宽度
            default_widths = [120, 60, 60, 60]
            for col, width in enumerate(default_widths):
                if col < tree.columnCount():
                    tree.setColumnWidth(col, width)
            self.logger.warning(f"Sheet树响应式列宽设置失败，使用默认宽度: {e}")
    
    def _create_strategy_group(self) -> QGroupBox:
        """创建导入策略组"""
        strategy_group = QGroupBox("导入策略")
        strategy_layout = QVBoxLayout(strategy_group)
        
        # 策略选择单选按钮
        from PyQt5.QtWidgets import QRadioButton, QButtonGroup
        
        self.strategy_group = QButtonGroup()
        
        self.merge_radio = QRadioButton("📝 合并到单表")
        self.merge_radio.setChecked(True)
        self.merge_radio.setToolTip("将所有选中的Sheet数据合并到一个表中")
        
        self.separate_radio = QRadioButton("📄 分别创建表")
        self.separate_radio.setToolTip("为每个Sheet创建独立的数据表")
        
        self.strategy_group.addButton(self.merge_radio, 0)
        self.strategy_group.addButton(self.separate_radio, 1)
        
        strategy_layout.addWidget(self.merge_radio)
        strategy_layout.addWidget(self.separate_radio)
        
        # 策略描述
        self.strategy_desc = QLabel("将多个Sheet的数据合并到一个统一的表中")
        self.strategy_desc.setStyleSheet("color: #666; font-size: 11px; padding: 5px;")
        self.strategy_desc.setWordWrap(True)
        strategy_layout.addWidget(self.strategy_desc)
        
        return strategy_group
    
    def _connect_signals(self):
        """连接信号"""
        # 工具栏按钮
        self.select_all_btn.clicked.connect(self._select_all_sheets)
        self.deselect_all_btn.clicked.connect(self._deselect_all_sheets)
        self.refresh_btn.clicked.connect(self._refresh_sheets)
        self.preview_btn.clicked.connect(self._preview_selected_sheet)
        self.analyze_btn.clicked.connect(self._analyze_sheets)
        
        # Sheet树选择变化
        self.sheet_tree.itemChanged.connect(self._on_sheet_selection_changed)
        self.sheet_tree.currentItemChanged.connect(self._on_current_sheet_changed)
        
        # 策略变化
        self.strategy_group.buttonClicked.connect(self._on_strategy_changed)
    
    def load_sheets(self, sheet_info: List[Dict]):
        """加载Sheet信息"""
        self.sheet_info = sheet_info
        self.sheet_tree.clear()
        
        from PyQt5.QtWidgets import QTreeWidgetItem
        
        for sheet in sheet_info:
            item = QTreeWidgetItem()
            item.setText(0, sheet['name'])
            
            # 状态显示
            status = sheet.get('status', 'ready')
            if status == 'ready':
                item.setText(1, "✅")
                item.setToolTip(1, "就绪")
            elif 'error' in status:
                item.setText(1, "❌")
                item.setToolTip(1, f"错误: {status}")
            else:
                item.setText(1, "⏳")
                item.setToolTip(1, "处理中")
            
            # 记录数
            row_count = sheet.get('row_count', 0)
            item.setText(2, str(row_count))
            
            # 操作按钮
            if row_count > 0:
                item.setText(3, "📊")
                item.setToolTip(3, "点击预览数据")
            else:
                item.setText(3, "❌")
                item.setToolTip(3, "无数据")
            
            # 设置复选框
            item.setFlags(item.flags() | Qt.ItemIsUserCheckable)
            item.setCheckState(0, Qt.Checked if sheet.get('enabled', True) else Qt.Unchecked)
            
            # 存储原始数据
            item.setData(0, Qt.UserRole, sheet)
            
            self.sheet_tree.addTopLevelItem(item)
        
        self.status_label.setText(f"已加载 {len(sheet_info)} 个工作表")
        self.logger.info(f"已加载 {len(sheet_info)} 个Sheet")
        
        # 触发选择变化事件
        self._on_sheet_selection_changed()
    
    def _select_all_sheets(self):
        """全选Sheet"""
        count = 0
        for i in range(self.sheet_tree.topLevelItemCount()):
            item = self.sheet_tree.topLevelItem(i)
            item.setCheckState(0, Qt.Checked)
            count += 1

        if count > 0:
            self.status_label.setText(f"✅ 已全选 {count} 个工作表")
            self.status_label.setStyleSheet("color: green; font-weight: bold;")
        else:
            self.status_label.setText("⚠️ 没有可选择的工作表，请先选择Excel文件")
            self.status_label.setStyleSheet("color: orange; font-weight: bold;")
    
    def _deselect_all_sheets(self):
        """取消全选"""
        count = 0
        for i in range(self.sheet_tree.topLevelItemCount()):
            item = self.sheet_tree.topLevelItem(i)
            item.setCheckState(0, Qt.Unchecked)
            count += 1

        if count > 0:
            self.status_label.setText(f"✅ 已取消选择 {count} 个工作表")
            self.status_label.setStyleSheet("color: blue; font-weight: bold;")
        else:
            self.status_label.setText("⚠️ 没有可取消的工作表")
            self.status_label.setStyleSheet("color: orange; font-weight: bold;")
    
    def _refresh_sheets(self):
        """刷新Sheet信息"""
        if self.sheet_info:
            self.load_sheets(self.sheet_info)
            self.status_label.setText(f"✅ 工作表信息已刷新 ({len(self.sheet_info)} 个工作表)")
            self.status_label.setStyleSheet("color: green; font-weight: bold;")
        else:
            self.status_label.setText("⚠️ 没有可刷新的工作表信息，请先选择Excel文件")
            self.status_label.setStyleSheet("color: orange; font-weight: bold;")
    
    def _preview_selected_sheet(self):
        """预览选中的Sheet"""
        current_item = self.sheet_tree.currentItem()
        if current_item:
            sheet_data = current_item.data(0, Qt.UserRole)
            if sheet_data:
                self.sheet_preview_requested.emit(sheet_data['name'])
                self.status_label.setText(f"✅ 正在预览工作表: {sheet_data['name']}")
                self.status_label.setStyleSheet("color: blue; font-weight: bold;")
            else:
                self.status_label.setText("❌ 无法获取工作表数据")
                self.status_label.setStyleSheet("color: red; font-weight: bold;")
        else:
            self.status_label.setText("⚠️ 请先选择要预览的工作表")
            self.status_label.setStyleSheet("color: orange; font-weight: bold;")
    
    def _analyze_sheets(self):
        """分析Sheet结构"""
        selected_sheets = self.get_selected_sheets()
        if selected_sheets:
            # TODO: 实现Sheet结构分析
            self.status_label.setText(f"🔍 正在分析 {len(selected_sheets)} 个工作表的结构...")
            self.status_label.setStyleSheet("color: blue; font-weight: bold;")
            # 这里可以添加实际的分析逻辑
        else:
            self.status_label.setText("⚠️ 请先选择要分析的工作表")
            self.status_label.setStyleSheet("color: orange; font-weight: bold;")
    
    def _on_sheet_selection_changed(self):
        """Sheet选择变化处理"""
        selected_sheets = self.get_selected_sheets()
        self.sheet_selection_changed.emit(selected_sheets)
        
        # 更新状态
        if selected_sheets:
            self.status_label.setText(f"已选择 {len(selected_sheets)} 个工作表")
        else:
            self.status_label.setText("未选择工作表")
    
    def _on_current_sheet_changed(self, current, previous):
        """当前Sheet变化处理"""
        if current:
            sheet_data = current.data(0, Qt.UserRole)
            if sheet_data:
                self.logger.info(f"选中Sheet: {sheet_data['name']}")
    
    def _on_strategy_changed(self, button):
        """导入策略变化处理"""
        if button == self.merge_radio:
            self.current_strategy = "merge_to_single_table"
            self.strategy_desc.setText("将多个Sheet的数据合并到一个统一的表中，适用于结构相似的数据")
        else:
            self.current_strategy = "separate_tables"
            self.strategy_desc.setText("为每个Sheet创建独立的数据表，适用于结构不同的数据")
        
        self.import_strategy_changed.emit(self.current_strategy)
        self.logger.info(f"导入策略变更为: {self.current_strategy}")
    
    def get_selected_sheets(self) -> List[Dict]:
        """获取选中的Sheet列表"""
        selected = []
        for i in range(self.sheet_tree.topLevelItemCount()):
            item = self.sheet_tree.topLevelItem(i)
            if item.checkState(0) == Qt.Checked:
                sheet_data = item.data(0, Qt.UserRole)
                if sheet_data:
                    selected.append(sheet_data)
        return selected
    
    def get_import_strategy(self) -> str:
        """获取当前导入策略"""
        return self.current_strategy


class UnifiedMappingConfigWidget(QWidget):
    """统一映射配置组件"""
    
    # 信号定义
    mapping_changed = pyqtSignal()  # 映射配置变化信号
    validation_completed = pyqtSignal(bool)  # 验证完成信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = setup_logger(__name__)
        
        # 初始化数据
        self.excel_headers = []
        self.current_table_type = ""
        self.mapping_config = {}
        self.validation_results = {}
        
        # 初始化核心组件
        from src.gui.core import SmartMappingEngine, TemplateManager, ValidationEngine
        from src.gui.performance_optimizer import get_performance_optimizer
        
        self.mapping_engine = SmartMappingEngine()
        self.template_manager = TemplateManager()
        self.validation_engine = ValidationEngine()
        self.performance_optimizer = get_performance_optimizer()
        
        self._init_ui()
        self._connect_signals()
    
    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 映射工具栏
        toolbar = self._create_mapping_toolbar()
        layout.addWidget(toolbar)
        
        # 映射配置表格
        self.mapping_table = self._create_mapping_table()
        layout.addWidget(self.mapping_table, 1)
        
        # 状态栏
        status_layout = QHBoxLayout()
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("color: #666; font-size: 12px;")
        self.validation_label = QLabel("")
        self.validation_label.setStyleSheet("font-weight: bold;")
        
        status_layout.addWidget(self.status_label)
        status_layout.addStretch()
        status_layout.addWidget(self.validation_label)
        
        layout.addLayout(status_layout)
    
    def _create_mapping_toolbar(self) -> QWidget:
        """创建映射工具栏"""
        toolbar = QFrame()
        toolbar.setFrameStyle(QFrame.StyledPanel)
        toolbar.setMaximumHeight(45)  # 恢复单行高度

        layout = QHBoxLayout(toolbar)
        layout.setContentsMargins(10, 5, 10, 5)
        layout.setSpacing(8)

        # 所有按钮一行显示
        self.smart_mapping_btn = QPushButton("智能映射")
        self.save_template_btn = QPushButton("保存模板")
        self.load_template_btn = QPushButton("加载模板")
        self.reset_mapping_btn = QPushButton("重置映射")
        self.validate_btn = QPushButton("验证配置")

        # 设置按钮样式
        for btn in [self.smart_mapping_btn, self.save_template_btn, self.load_template_btn,
                   self.reset_mapping_btn, self.validate_btn]:
            btn.setMinimumHeight(32)
            btn.setMaximumWidth(90)

        # 设置按钮工具提示
        self.smart_mapping_btn.setToolTip("基于字段名称智能推荐映射关系")
        self.save_template_btn.setToolTip("将当前映射配置保存为模板")
        self.load_template_btn.setToolTip("从已保存的模板加载映射配置")
        self.reset_mapping_btn.setToolTip("重置所有映射配置到初始状态")
        self.validate_btn.setToolTip("验证当前映射配置的正确性")

        # 重要按钮高亮
        self.smart_mapping_btn.setStyleSheet(
            "QPushButton { background-color: #2196F3; color: white; font-weight: bold; }"
            "QPushButton:hover { background-color: #1976D2; }"
        )

        layout.addWidget(self.smart_mapping_btn)
        layout.addWidget(self.save_template_btn)
        layout.addWidget(self.load_template_btn)
        layout.addWidget(self.reset_mapping_btn)
        layout.addWidget(self.validate_btn)
        layout.addStretch()

        return toolbar
    
    def _create_mapping_table(self) -> QTableWidget:
        """创建映射配置表格"""
        table = QTableWidget()
        table.setColumnCount(6)
        table.setHorizontalHeaderLabels([
            "Excel列名", "数据库字段", "显示名称", "数据类型", "是否必需", "验证状态"
        ])
        
        # 设置响应式列宽（初始设置，会在窗口大小变化时动态调整）
        self._setup_table_responsive_columns(table)
        
        # 设置表格属性
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectRows)
        table.setSelectionMode(QTableWidget.SingleSelection)
        table.verticalHeader().setVisible(False)
        
        # 设置编辑触发器 - 允许双击和选中后按键编辑
        table.setEditTriggers(QTableWidget.DoubleClicked | QTableWidget.SelectedClicked | QTableWidget.EditKeyPressed)

        # 设置行高以确保编辑器有足够空间
        table.verticalHeader().setDefaultSectionSize(35)
        
        # 设置简洁美观的表格样式，只在编辑时显示特殊效果
        table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e9ecef;
                background-color: #ffffff;
                alternate-background-color: #f8f9fa;
                selection-background-color: #cfe2ff;
                selection-color: #0d6efd;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                font-size: 12px;
            }
            
            QTableWidget::item {
                padding: 8px;
                border: none;
                border-bottom: 1px solid #f1f3f4;
            }
            
            QTableWidget::item:selected {
                background-color: #cfe2ff;
                color: #0d6efd;
            }
            
            QTableWidget::item:hover {
                background-color: #e7f1ff;
            }
            
            /* 表头样式 */
            QHeaderView::section {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #f8f9fa, stop: 1 #e9ecef);
                padding: 10px 8px;
                border: 1px solid #dee2e6;
                border-radius: 0px;
                font-weight: 600;
                font-size: 12px;
                color: #495057;
            }
            
            QHeaderView::section:first {
                border-top-left-radius: 6px;
            }
            
            QHeaderView::section:last {
                border-top-right-radius: 6px;
            }
            
            /* 编辑时的低调提示 - 只改变背景色，不加边框 */
            QLineEdit {
                background-color: #fff9c4;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 6px 8px;
                font-size: 12px;
                color: #495057;
                min-height: 18px;
            }
            
            QLineEdit:focus {
                background-color: #fff9c4;
                border-color: #ffc107;
                outline: none;
            }
        """)
        
        # 设置表格自适应策略
        header = table.horizontalHeader()
        header.setStretchLastSection(False)  # 最后一列不自动拉伸
        header.setSectionResizeMode(header.Interactive)  # 允许用户调整列宽
        
        return table
    
    def _setup_table_responsive_columns(self, table: QTableWidget):
        """设置表格响应式列宽"""
        try:
            # 获取表格父容器宽度，如果未初始化则使用默认值
            parent_width = table.parent().width() if table.parent() else 800
            if parent_width < 400:
                parent_width = 800  # 默认宽度
            
            # 预留滚动条和边距的宽度
            available_width = parent_width - 40
            
            # 定义各列的相对宽度比例
            column_ratios = [0.25, 0.25, 0.20, 0.15, 0.10, 0.05]

            # 定义各列的最小宽度
            min_widths = [120, 120, 100, 80, 70, 50]
            
            # 计算并设置列宽
            for col, (ratio, min_width) in enumerate(zip(column_ratios, min_widths)):
                calculated_width = int(available_width * ratio)
                final_width = max(calculated_width, min_width)
                table.setColumnWidth(col, final_width)
            
            self.logger.debug(f"表格响应式列宽设置完成: 可用宽度={available_width}px")
            
        except Exception as e:
            # 如果响应式设置失败，使用默认固定宽度
            default_widths = [150, 150, 120, 100, 80, 80]
            for col, width in enumerate(default_widths):
                if col < table.columnCount():
                    table.setColumnWidth(col, width)
            self.logger.warning(f"响应式列宽设置失败，使用默认宽度: {e}")
    
    def _connect_signals(self):
        """连接信号"""
        # 工具栏按钮
        self.smart_mapping_btn.clicked.connect(self._generate_smart_mapping)
        self.save_template_btn.clicked.connect(self._save_as_template)
        self.load_template_btn.clicked.connect(self._load_from_template)
        self.reset_mapping_btn.clicked.connect(self._reset_mapping)
        self.validate_btn.clicked.connect(self._validate_mapping)
        
        # 表格变化
        self.mapping_table.cellChanged.connect(self._on_mapping_changed)
    
    def _clean_field_name(self, field_name: str) -> str:
        """清理字段名，移除特殊字符"""
        import re

        if not field_name:
            return "field_name"

        # 移除所有空白字符（换行符、制表符、空格等）
        cleaned = re.sub(r'\s+', '', field_name.strip())

        # 移除特殊字符，只保留字母、数字、下划线和中文
        cleaned = re.sub(r'[^\w\u4e00-\u9fff]', '', cleaned)

        # 如果清理后为空，使用默认名称
        if not cleaned:
            cleaned = "field_name"

        # 确保不以数字开头（数据库字段名规范）
        if cleaned and cleaned[0].isdigit():
            cleaned = f"field_{cleaned}"

        return cleaned

    def load_excel_headers(self, headers: List[str], table_type: str):
        """加载Excel字段头"""
        self.excel_headers = headers
        self.current_table_type = table_type
        self.mapping_config = {}

        self.logger.info(f"加载Excel字段: {len(headers)} 个字段, 表类型: {table_type}")

        # 临时断开信号连接，避免在创建表格时触发变化事件
        self.mapping_table.cellChanged.disconnect()

        # 创建表格行
        self.mapping_table.setRowCount(len(headers))
        
        for row, header in enumerate(headers):
            # Excel列名（只读）
            excel_item = QTableWidgetItem(header)
            excel_item.setFlags(excel_item.flags() & ~Qt.ItemIsEditable)
            self.mapping_table.setItem(row, 0, excel_item)

            # 数据库字段（输入框）- 默认值为清理后的Excel列名
            cleaned_field_name = self._clean_field_name(header)
            db_field_item = QTableWidgetItem(cleaned_field_name)
            db_field_item.setFlags(Qt.ItemIsEnabled | Qt.ItemIsEditable | Qt.ItemIsSelectable)
            db_field_item.setToolTip(f"原字段名: {header}\n清理后: {cleaned_field_name}")
            self.mapping_table.setItem(row, 1, db_field_item)
            
            # 显示名称（可编辑）
            display_item = QTableWidgetItem(header)
            self.mapping_table.setItem(row, 2, display_item)
            
            # 数据类型（下拉框）
            type_combo = QComboBox()
            type_combo.addItems(["VARCHAR(100)", "VARCHAR(255)", "INT", "DECIMAL(10,2)", "DATE", "TEXT"])
            type_combo.setCurrentText("VARCHAR(100)")
            self.mapping_table.setCellWidget(row, 3, type_combo)
            
            # 是否必需（复选框）
            required_item = QTableWidgetItem()
            required_item.setCheckState(Qt.Unchecked)
            required_item.setFlags(required_item.flags() & ~Qt.ItemIsEditable)
            self.mapping_table.setItem(row, 4, required_item)
            
            # 验证状态（只读图标）
            status_item = QTableWidgetItem("⏳")
            status_item.setFlags(status_item.flags() & ~Qt.ItemIsEditable)
            self.mapping_table.setItem(row, 5, status_item)
            
        self.status_label.setText(f"已加载 {len(headers)} 个字段")

        # 重新连接信号（在表格完全创建后）
        self.mapping_table.cellChanged.connect(self._on_mapping_changed)

        # 连接下拉框信号
        for row in range(self.mapping_table.rowCount()):
            type_combo = self.mapping_table.cellWidget(row, 3)
            if type_combo:
                type_combo.currentTextChanged.connect(self._on_mapping_changed)

        # 重新调整列宽以确保编辑体验
        self._setup_table_responsive_columns(self.mapping_table)

        self.logger.info(f"字段映射表格创建完成: {len(headers)} 行")
        
        # 自动生成智能映射
        if headers:
            self._generate_smart_mapping()
    
    def _generate_smart_mapping(self):
        """生成智能映射"""
        if not self.excel_headers or not self.current_table_type:
            self.status_label.setText("⚠️ 请先选择Excel文件和表类型")
            self.status_label.setStyleSheet("color: orange; font-weight: bold;")
            return

        try:
            self.status_label.setText("🤖 正在分析字段并生成智能映射...")
            self.status_label.setStyleSheet("color: blue; font-weight: bold;")

            # 使用智能映射引擎生成建议
            mapping_results = self.mapping_engine.generate_smart_mapping(
                self.excel_headers, self.current_table_type
            )

            applied_count = 0
            high_confidence_count = 0

            # 应用映射结果到表格
            for result in mapping_results:
                row = self.excel_headers.index(result.source_field)
                
                # 更新数据库字段
                db_field_item = self.mapping_table.item(row, 1)
                if db_field_item:
                    db_field_item.setText(result.target_field)
                
                # 更新显示名称
                display_item = self.mapping_table.item(row, 2)
                if display_item:
                    display_item.setText(result.display_name)
                
                # 更新数据类型
                type_combo = self.mapping_table.cellWidget(row, 3)
                if type_combo:
                    type_combo.setCurrentText(result.data_type)
                
                # 更新是否必需
                required_item = self.mapping_table.item(row, 4)
                if required_item:
                    required_item.setCheckState(Qt.Checked if result.is_required else Qt.Unchecked)
                
                # 更新验证状态（基于置信度）
                status_item = self.mapping_table.item(row, 5)
                if status_item:
                    if result.confidence >= 0.8:
                        status_item.setText("✅")
                        status_item.setToolTip(f"高置信度 ({result.confidence:.1%}): {result.reasoning}")
                        high_confidence_count += 1
                    elif result.confidence >= 0.5:
                        status_item.setText("⚠️")
                        status_item.setToolTip(f"中等置信度 ({result.confidence:.1%}): {result.reasoning}")
                    else:
                        status_item.setText("❓")
                        status_item.setToolTip(f"低置信度 ({result.confidence:.1%}): {result.reasoning}")

                applied_count += 1

            self._update_mapping_config()

            # 显示详细结果
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.information(
                self, "智能映射完成",
                f"智能映射已完成！\n\n"
                f"• 总共处理字段: {len(self.excel_headers)} 个\n"
                f"• 成功映射字段: {applied_count} 个\n"
                f"• 高置信度映射: {high_confidence_count} 个\n"
                f"• 建议人工检查: {applied_count - high_confidence_count} 个"
            )

            self.status_label.setText(f"✅ 智能映射完成: {applied_count}/{len(self.excel_headers)} 个字段")
            self.status_label.setStyleSheet("color: green; font-weight: bold;")
            self.logger.info("智能映射生成成功")

        except Exception as e:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(self, "错误", f"智能映射失败: {str(e)}")
            self.status_label.setText("❌ 智能映射失败")
            self.status_label.setStyleSheet("color: red; font-weight: bold;")
            self.logger.error(f"智能映射生成失败: {e}")
    
    def _save_as_template(self):
        """增强模板保存功能"""
        from PyQt5.QtWidgets import QInputDialog, QDialog, QVBoxLayout, QLineEdit, QTextEdit, QPushButton, QHBoxLayout, QLabel, QComboBox
        
        if not self.mapping_config:
            self.status_label.setText("请先配置字段映射")
            return
        
        # 创建增强的模板保存对话框
        dialog = QDialog(self)
        dialog.setWindowTitle("保存映射模板")
        dialog.setFixedSize(450, 320)
        
        layout = QVBoxLayout(dialog)
        
        # 模板名称
        name_layout = QHBoxLayout()
        name_layout.addWidget(QLabel("模板名称:"))
        name_edit = QLineEdit()
        name_edit.setPlaceholderText("请输入模板名称")
        name_layout.addWidget(name_edit)
        layout.addLayout(name_layout)
        
        # 模板描述
        desc_layout = QVBoxLayout()
        desc_layout.addWidget(QLabel("模板描述:"))
        desc_edit = QTextEdit()
        desc_edit.setPlaceholderText("请输入模板描述（可选）")
        desc_edit.setMaximumHeight(60)
        desc_layout.addWidget(desc_edit)
        layout.addLayout(desc_layout)
        
        # 适用表类型
        type_layout = QHBoxLayout()
        type_layout.addWidget(QLabel("适用类型:"))
        type_combo = QComboBox()
        type_combo.addItems(["💰 工资表", "🔄 异动表", "📊 通用表"])
        type_combo.setCurrentText(self.current_table_type)
        type_layout.addWidget(type_combo)
        layout.addLayout(type_layout)
        
        # 模板作用域
        scope_layout = QHBoxLayout()
        scope_layout.addWidget(QLabel("作用域:"))
        scope_combo = QComboBox()
        scope_combo.addItems(["个人模板", "团队共享", "全局模板"])
        scope_layout.addWidget(scope_combo)
        layout.addLayout(scope_layout)
        
        # 字段预览
        preview_label = QLabel(f"将保存 {len(self.mapping_config)} 个字段映射:")
        preview_label.setStyleSheet("font-weight: bold; margin-top: 10px;")
        layout.addWidget(preview_label)
        
        preview_text = QTextEdit()
        preview_content = []
        for excel_field, config in list(self.mapping_config.items())[:5]:  # 只显示前5个
            target = config.get('target_field', excel_field)
            data_type = config.get('data_type', 'VARCHAR(100)')
            preview_content.append(f"• {excel_field} → {target} ({data_type})")
        
        if len(self.mapping_config) > 5:
            preview_content.append(f"... 还有 {len(self.mapping_config) - 5} 个字段")
        
        preview_text.setPlainText('\n'.join(preview_content))
        preview_text.setMaximumHeight(100)
        preview_text.setReadOnly(True)
        layout.addWidget(preview_text)
        
        # 按钮
        button_layout = QHBoxLayout()
        save_btn = QPushButton("💾 保存模板")
        cancel_btn = QPushButton("❌ 取消")
        
        button_layout.addWidget(save_btn)
        button_layout.addWidget(cancel_btn)
        layout.addLayout(button_layout)
        
        # 连接信号
        save_btn.clicked.connect(dialog.accept)
        cancel_btn.clicked.connect(dialog.reject)
        
        if dialog.exec_() == QDialog.Accepted:
            template_name = name_edit.text().strip()
            if not template_name:
                self.status_label.setText("请输入模板名称")
                return
            
            try:
                # 创建增强模板
                template_data = {
                    'name': template_name,
                    'description': desc_edit.toPlainText().strip(),
                    'table_type': type_combo.currentText(),
                    'scope': scope_combo.currentText(),
                    'mapping_config': self.mapping_config,
                    'field_count': len(self.mapping_config),
                    'created_by': 'current_user',  # TODO: 获取当前用户
                    'version': '1.0'
                }
                
                # 保存模板
                if self.template_manager.save_enhanced_template(template_data):
                    self.status_label.setText(f"模板 '{template_name}' 保存成功")
                    self.logger.info(f"增强模板保存成功: {template_name}")
                    
                    # 保存用户映射用于学习
                    self.mapping_engine.save_user_mapping(self.current_table_type, self.mapping_config)
                else:
                    self.status_label.setText("模板保存失败")
                    
            except Exception as e:
                error_msg = f"保存模板失败: {e}"
                self.status_label.setText(error_msg)
                self.logger.error(error_msg)
    
    def _load_from_template(self):
        """从模板加载"""
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QListWidget, QPushButton, QHBoxLayout
        
        # 创建模板选择对话框
        dialog = QDialog(self)
        dialog.setWindowTitle("选择模板")
        dialog.setFixedSize(400, 300)
        
        layout = QVBoxLayout(dialog)
        
        # 模板列表
        template_list = QListWidget()
        templates = self.template_manager.get_all_templates()
        
        for template in templates:
            item_text = f"{template.name} ({template.type.value})"
            template_list.addItem(item_text)
            template_list.item(template_list.count() - 1).setData(Qt.UserRole, template)
        
        layout.addWidget(template_list)
        
        # 按钮
        button_layout = QHBoxLayout()
        load_btn = QPushButton("加载")
        cancel_btn = QPushButton("取消")
        
        button_layout.addWidget(load_btn)
        button_layout.addWidget(cancel_btn)
        layout.addLayout(button_layout)
        
        # 连接信号
        load_btn.clicked.connect(dialog.accept)
        cancel_btn.clicked.connect(dialog.reject)
        
        if dialog.exec_() == QDialog.Accepted:
            current_item = template_list.currentItem()
            if current_item:
                template = current_item.data(Qt.UserRole)
                self._apply_template(template)
    
    def _apply_template(self, template):
        """应用模板"""
        try:
            # 应用模板字段到表格
            for row in range(self.mapping_table.rowCount()):
                excel_field = self.mapping_table.item(row, 0).text()
                
                # 在模板中查找匹配的字段
                matching_field = None
                for field in template.fields:
                    if field.field_name == excel_field or field.display_name == excel_field:
                        matching_field = field
                        break
                
                if matching_field:
                    # 应用模板字段配置
                    db_field_item = self.mapping_table.item(row, 1)
                    if db_field_item:
                        db_field_item.setText(matching_field.field_name)
                    
                    display_item = self.mapping_table.item(row, 2)
                    if display_item:
                        display_item.setText(matching_field.display_name)
                    
                    type_combo = self.mapping_table.cellWidget(row, 3)
                    if type_combo:
                        type_combo.setCurrentText(matching_field.data_type)
                    
                    required_item = self.mapping_table.item(row, 4)
                    if required_item:
                        required_item.setCheckState(Qt.Checked if matching_field.is_required else Qt.Unchecked)
            
            self._update_mapping_config()
            self.status_label.setText(f"模板 '{template.name}' 应用成功")
            self.logger.info(f"模板应用成功: {template.name}")
            
        except Exception as e:
            error_msg = f"应用模板失败: {e}"
            self.status_label.setText(error_msg)
            self.logger.error(error_msg)
    
    def _reset_mapping(self):
        """重置映射"""
        if self.mapping_config:
            from PyQt5.QtWidgets import QMessageBox
            reply = QMessageBox.question(
                self, "确认重置",
                "确定要重置所有映射配置吗？此操作不可撤销。",
                QMessageBox.Yes | QMessageBox.No
            )
            if reply == QMessageBox.Yes:
                self.load_excel_headers(self.excel_headers, self.current_table_type)
                self.status_label.setText("✅ 映射配置已重置")
                self.status_label.setStyleSheet("color: green; font-weight: bold;")
        else:
            self.status_label.setText("⚠️ 没有可重置的映射配置")
            self.status_label.setStyleSheet("color: orange; font-weight: bold;")
    
    def _validate_mapping(self):
        """验证映射配置"""
        if not self.mapping_config:
            self.status_label.setText("⚠️ 请先配置字段映射")
            self.status_label.setStyleSheet("color: orange; font-weight: bold;")
            return
        
        try:
            self.status_label.setText("正在验证映射配置...")
            
            # 使用验证引擎验证
            report = self.validation_engine.validate_import_configuration(
                self.mapping_config, self.current_table_type
            )
            
            # 更新验证状态显示
            if report.is_valid:
                self.validation_label.setText("✅ 验证通过")
                self.validation_label.setStyleSheet("color: green; font-weight: bold;")
                self.validation_completed.emit(True)
            else:
                self.validation_label.setText(f"❌ {report.error_count} 个错误")
                self.validation_label.setStyleSheet("color: red; font-weight: bold;")
                self.validation_completed.emit(False)
            
            # 显示详细信息
            details = f"验证完成: {report.summary}"
            if report.errors:
                details += f"\n错误: {'; '.join(report.errors[:3])}"
            if report.warnings:
                details += f"\n警告: {'; '.join(report.warnings[:3])}"
            
            self.validation_label.setToolTip(details)
            self.status_label.setText(details.split('\n')[0])
            
            self.logger.info(f"验证完成: {report.summary}")
            
        except Exception as e:
            error_msg = f"验证失败: {e}"
            self.status_label.setText(error_msg)
            self.validation_label.setText("❌ 验证失败")
            self.validation_label.setStyleSheet("color: red; font-weight: bold;")
            self.logger.error(error_msg)
    
    def _on_mapping_changed(self):
        """映射配置变化处理"""
        self._update_mapping_config()
        self.mapping_changed.emit()
    
    def _update_mapping_config(self):
        """更新映射配置"""
        self.mapping_config = {}
        
        for row in range(self.mapping_table.rowCount()):
            # 获取Excel字段名
            excel_item = self.mapping_table.item(row, 0)
            if not excel_item:
                continue
            excel_field = excel_item.text()
            
            # 获取数据库字段
            db_field_item = self.mapping_table.item(row, 1)
            db_field = db_field_item.text() if db_field_item else excel_field
            
            # 获取显示名称
            display_item = self.mapping_table.item(row, 2)
            display_name = display_item.text() if display_item else excel_field
            
            # 获取数据类型
            type_combo = self.mapping_table.cellWidget(row, 3)
            data_type = type_combo.currentText() if type_combo else "VARCHAR(100)"
            
            # 获取是否必需
            required_item = self.mapping_table.item(row, 4)
            is_required = required_item.checkState() == Qt.Checked if required_item else False
            
            self.mapping_config[excel_field] = {
                'target_field': db_field,
                'display_name': display_name,
                'data_type': data_type,
                'is_required': is_required
            }
    
    def get_mapping_config(self) -> Dict[str, Dict]:
        """获取当前映射配置"""
        return self.mapping_config.copy()
    
    def _on_advanced_config_changed(self, config: Dict[str, Any]):
        """高级配置变化处理"""
        try:
            # 应用智能推荐设置
            smart_config = config.get('smart_recommendations', {})
            
            # 更新智能映射引擎配置
            if hasattr(self.mapping_engine, 'update_config'):
                self.mapping_engine.update_config(smart_config)
            
            # 更新置信度阈值
            confidence_threshold = smart_config.get('confidence_threshold', 70) / 100.0
            
            # 如果启用了自动应用高置信度推荐
            if smart_config.get('auto_apply_high_confidence', False):
                self._auto_apply_high_confidence_mappings(confidence_threshold)
            
            # 应用UI设置
            ui_config = config.get('ui_customization', {})
            
            # 更新表格行数限制
            row_limit = ui_config.get('table_row_limit', 200)
            if hasattr(self, 'mapping_table'):
                # TODO: 应用行数限制到表格显示
                pass
            
            # 显示/隐藏置信度指示器
            show_confidence = ui_config.get('show_confidence_indicators', True)
            self._toggle_confidence_indicators(show_confidence)
            
            self.logger.info("高级配置已应用到映射组件")
            
        except Exception as e:
            self.logger.error(f"应用高级配置失败: {e}")
    
    def _auto_apply_high_confidence_mappings(self, threshold: float):
        """自动应用高置信度映射"""
        if not self.excel_headers:
            return
        
        try:
            # 重新生成智能映射
            mapping_results = self.mapping_engine.generate_smart_mapping(
                self.excel_headers, self.current_table_type
            )
            
            applied_count = 0
            for result in mapping_results:
                if result.confidence >= threshold:
                    row = self.excel_headers.index(result.source_field)
                    
                    # 自动应用高置信度映射
                    db_field_item = self.mapping_table.item(row, 1)
                    if db_field_item:
                        db_field_item.setText(result.target_field)
                        applied_count += 1
            
            if applied_count > 0:
                self.status_label.setText(f"自动应用了 {applied_count} 个高置信度映射")
                self._update_mapping_config()
            
        except Exception as e:
            self.logger.error(f"自动应用高置信度映射失败: {e}")
    
    def _toggle_confidence_indicators(self, show: bool):
        """切换置信度指示器显示"""
        if not hasattr(self, 'mapping_table'):
            return
        
        try:
            # 显示/隐藏验证状态列
            column_index = 5  # 验证状态列
            if show:
                self.mapping_table.showColumn(column_index)
            else:
                self.mapping_table.hideColumn(column_index)
                
        except Exception as e:
            self.logger.error(f"切换置信度指示器失败: {e}")


class DataProcessingWidget(QWidget):
    """数据处理选项卡组件"""

    # 信号定义
    processing_config_changed = pyqtSignal(dict)  # 处理配置变化信号

    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = setup_logger(__name__)

        # 初始化配置
        self.processing_config = {
            'remove_duplicates': False,
            'handle_missing_values': 'keep',  # keep, remove, fill_zero, fill_mean
            'data_validation': True,
            'format_numbers': True,
            'format_dates': True,
            'trim_whitespace': True,
            'convert_data_types': True,
            'custom_rules': []
        }

        self._init_ui()
        self._connect_signals()

    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)

        # 标题
        title_label = QLabel("数据处理配置")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #333;")
        layout.addWidget(title_label)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameStyle(QFrame.NoFrame)

        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        scroll_layout.setSpacing(10)

        # 数据清理组
        self._create_data_cleaning_group(scroll_layout)

        # 数据格式化组
        self._create_data_formatting_group(scroll_layout)

        # 数据验证组
        self._create_data_validation_group(scroll_layout)

        # 自定义规则组
        self._create_custom_rules_group(scroll_layout)

        scroll_layout.addStretch()
        scroll_area.setWidget(scroll_widget)
        layout.addWidget(scroll_area)

        # 底部操作按钮
        self._create_action_buttons(layout)

    def _create_data_cleaning_group(self, layout):
        """创建数据清理组"""
        group = QGroupBox("数据清理")
        group_layout = QVBoxLayout(group)

        # 去除重复数据
        self.remove_duplicates_cb = QCheckBox("去除重复数据")
        self.remove_duplicates_cb.setToolTip("删除完全相同的数据行")
        group_layout.addWidget(self.remove_duplicates_cb)

        # 处理缺失值
        missing_layout = QHBoxLayout()
        missing_layout.addWidget(QLabel("缺失值处理:"))

        self.missing_values_combo = QComboBox()
        self.missing_values_combo.addItem("保留空值", "keep")
        self.missing_values_combo.addItem("删除包含空值的行", "remove")
        self.missing_values_combo.addItem("用0填充", "fill_zero")
        self.missing_values_combo.addItem("用平均值填充", "fill_mean")
        self.missing_values_combo.setToolTip("选择如何处理数据中的空值")
        missing_layout.addWidget(self.missing_values_combo)
        missing_layout.addStretch()

        group_layout.addLayout(missing_layout)

        # 去除空白字符
        self.trim_whitespace_cb = QCheckBox("去除首尾空白字符")
        self.trim_whitespace_cb.setChecked(True)
        self.trim_whitespace_cb.setToolTip("删除文本字段开头和结尾的空格")
        group_layout.addWidget(self.trim_whitespace_cb)

        layout.addWidget(group)

    def _create_data_formatting_group(self, layout):
        """创建数据格式化组"""
        group = QGroupBox("数据格式化")
        group_layout = QVBoxLayout(group)

        # 数字格式化
        self.format_numbers_cb = QCheckBox("格式化数字字段")
        self.format_numbers_cb.setChecked(True)
        self.format_numbers_cb.setToolTip("自动识别并格式化工资、金额等数字字段")
        group_layout.addWidget(self.format_numbers_cb)

        # 日期格式化
        self.format_dates_cb = QCheckBox("格式化日期字段")
        self.format_dates_cb.setChecked(True)
        self.format_dates_cb.setToolTip("自动识别并标准化日期格式")
        group_layout.addWidget(self.format_dates_cb)

        # 数据类型转换
        self.convert_types_cb = QCheckBox("自动转换数据类型")
        self.convert_types_cb.setChecked(True)
        self.convert_types_cb.setToolTip("根据内容自动转换为合适的数据类型")
        group_layout.addWidget(self.convert_types_cb)

        layout.addWidget(group)

    def _create_data_validation_group(self, layout):
        """创建数据验证组"""
        group = QGroupBox("数据验证")
        group_layout = QVBoxLayout(group)

        # 启用数据验证
        self.data_validation_cb = QCheckBox("启用数据验证")
        self.data_validation_cb.setChecked(True)
        self.data_validation_cb.setToolTip("检查数据的完整性和合理性")
        group_layout.addWidget(self.data_validation_cb)

        # 验证规则说明
        rules_label = QLabel(
            "验证规则包括：\n"
            "• 工资数据范围检查（0-100万）\n"
            "• 员工编号格式检查\n"
            "• 必填字段完整性检查\n"
            "• 数据类型一致性检查"
        )
        rules_label.setStyleSheet("color: #666; font-size: 12px; margin-left: 20px;")
        group_layout.addWidget(rules_label)

        layout.addWidget(group)

    def _create_custom_rules_group(self, layout):
        """创建自定义规则组"""
        group = QGroupBox("自定义处理规则")
        group_layout = QVBoxLayout(group)

        # 规则列表
        self.rules_list = QListWidget()
        self.rules_list.setMaximumHeight(100)
        self.rules_list.setToolTip("自定义的数据处理规则列表")
        group_layout.addWidget(self.rules_list)

        # 添加规则按钮
        rules_button_layout = QHBoxLayout()

        self.add_rule_btn = QPushButton("添加规则")
        self.add_rule_btn.setToolTip("添加新的数据处理规则")
        self.edit_rule_btn = QPushButton("编辑规则")
        self.edit_rule_btn.setToolTip("编辑选中的处理规则")
        self.remove_rule_btn = QPushButton("删除规则")
        self.remove_rule_btn.setToolTip("删除选中的处理规则")

        rules_button_layout.addWidget(self.add_rule_btn)
        rules_button_layout.addWidget(self.edit_rule_btn)
        rules_button_layout.addWidget(self.remove_rule_btn)
        rules_button_layout.addStretch()

        group_layout.addLayout(rules_button_layout)
        layout.addWidget(group)

    def _create_action_buttons(self, layout):
        """创建操作按钮"""
        button_layout = QHBoxLayout()

        # 预览处理效果
        self.preview_processing_btn = QPushButton("预览处理效果")
        self.preview_processing_btn.setToolTip("预览数据处理后的效果，不实际修改数据")
        self.preview_processing_btn.setStyleSheet(
            "QPushButton { background-color: #2196F3; color: white; font-weight: bold; padding: 8px 16px; }"
            "QPushButton:hover { background-color: #1976D2; }"
        )

        # 重置配置
        self.reset_config_btn = QPushButton("重置配置")
        self.reset_config_btn.setToolTip("重置所有处理配置到默认值")

        # 保存配置
        self.save_config_btn = QPushButton("保存配置")
        self.save_config_btn.setToolTip("保存当前处理配置为模板")

        # 加载配置
        self.load_config_btn = QPushButton("加载配置")
        self.load_config_btn.setToolTip("从已保存的模板加载处理配置")

        button_layout.addWidget(self.preview_processing_btn)
        button_layout.addStretch()
        button_layout.addWidget(self.reset_config_btn)
        button_layout.addWidget(self.save_config_btn)
        button_layout.addWidget(self.load_config_btn)

        layout.addLayout(button_layout)

        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("color: #666; font-size: 12px; margin-top: 10px;")
        layout.addWidget(self.status_label)

    def _connect_signals(self):
        """连接信号"""
        # 配置变化信号
        self.remove_duplicates_cb.toggled.connect(self._on_config_changed)
        self.missing_values_combo.currentTextChanged.connect(self._on_config_changed)
        self.trim_whitespace_cb.toggled.connect(self._on_config_changed)
        self.format_numbers_cb.toggled.connect(self._on_config_changed)
        self.format_dates_cb.toggled.connect(self._on_config_changed)
        self.convert_types_cb.toggled.connect(self._on_config_changed)
        self.data_validation_cb.toggled.connect(self._on_config_changed)

        # 按钮信号
        self.preview_processing_btn.clicked.connect(self._preview_processing)
        self.reset_config_btn.clicked.connect(self._reset_config)
        self.save_config_btn.clicked.connect(self._save_config)
        self.load_config_btn.clicked.connect(self._load_config)

        # 自定义规则按钮
        self.add_rule_btn.clicked.connect(self._add_custom_rule)
        self.edit_rule_btn.clicked.connect(self._edit_custom_rule)
        self.remove_rule_btn.clicked.connect(self._remove_custom_rule)

    def _on_config_changed(self):
        """配置变化处理"""
        self._update_processing_config()
        self.processing_config_changed.emit(self.processing_config)
        self.status_label.setText("配置已更新")
        self.status_label.setStyleSheet("color: green; font-weight: bold;")

    def _update_processing_config(self):
        """更新处理配置"""
        self.processing_config.update({
            'remove_duplicates': self.remove_duplicates_cb.isChecked(),
            'handle_missing_values': self.missing_values_combo.currentData() or 'keep',
            'data_validation': self.data_validation_cb.isChecked(),
            'format_numbers': self.format_numbers_cb.isChecked(),
            'format_dates': self.format_dates_cb.isChecked(),
            'trim_whitespace': self.trim_whitespace_cb.isChecked(),
            'convert_data_types': self.convert_types_cb.isChecked(),
        })

    def _preview_processing(self):
        """预览处理效果"""
        try:
            self.status_label.setText("🔍 正在预览数据处理效果...")
            self.status_label.setStyleSheet("color: blue; font-weight: bold;")

            # TODO: 实现数据处理预览逻辑
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.information(
                self, "预览处理效果",
                f"数据处理配置预览：\n\n"
                f"• 去除重复数据: {'是' if self.processing_config['remove_duplicates'] else '否'}\n"
                f"• 缺失值处理: {self._get_missing_value_text()}\n"
                f"• 数据验证: {'启用' if self.processing_config['data_validation'] else '禁用'}\n"
                f"• 格式化数字: {'是' if self.processing_config['format_numbers'] else '否'}\n"
                f"• 格式化日期: {'是' if self.processing_config['format_dates'] else '否'}\n"
                f"• 去除空白: {'是' if self.processing_config['trim_whitespace'] else '否'}\n"
                f"• 类型转换: {'是' if self.processing_config['convert_data_types'] else '否'}"
            )

            self.status_label.setText("✅ 预览完成")
            self.status_label.setStyleSheet("color: green; font-weight: bold;")

        except Exception as e:
            self.status_label.setText(f"❌ 预览失败: {str(e)}")
            self.status_label.setStyleSheet("color: red; font-weight: bold;")
            self.logger.error(f"预览处理效果失败: {e}")

    def _get_missing_value_text(self):
        """获取缺失值处理方式的中文描述"""
        mapping = {
            'keep': '保留空值',
            'remove': '删除包含空值的行',
            'fill_zero': '用0填充',
            'fill_mean': '用平均值填充'
        }
        return mapping.get(self.processing_config['handle_missing_values'], '保留空值')

    def _reset_config(self):
        """重置配置"""
        from PyQt5.QtWidgets import QMessageBox
        reply = QMessageBox.question(
            self, "确认重置",
            "确定要重置所有数据处理配置到默认值吗？",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # 重置到默认配置
            self.remove_duplicates_cb.setChecked(False)
            self.missing_values_combo.setCurrentIndex(0)  # 保留空值
            self.trim_whitespace_cb.setChecked(True)
            self.format_numbers_cb.setChecked(True)
            self.format_dates_cb.setChecked(True)
            self.convert_types_cb.setChecked(True)
            self.data_validation_cb.setChecked(True)

            # 清空自定义规则
            self.rules_list.clear()

            self.status_label.setText("✅ 配置已重置到默认值")
            self.status_label.setStyleSheet("color: green; font-weight: bold;")

    def _save_config(self):
        """保存配置"""
        from PyQt5.QtWidgets import QInputDialog, QMessageBox

        name, ok = QInputDialog.getText(
            self, "保存配置", "请输入配置模板名称:"
        )

        if ok and name:
            try:
                # TODO: 实现配置保存逻辑
                self.status_label.setText(f"✅ 配置已保存为模板: {name}")
                self.status_label.setStyleSheet("color: green; font-weight: bold;")

                QMessageBox.information(
                    self, "保存成功",
                    f"数据处理配置已保存为模板: {name}"
                )

            except Exception as e:
                self.status_label.setText(f"❌ 保存失败: {str(e)}")
                self.status_label.setStyleSheet("color: red; font-weight: bold;")
                self.logger.error(f"保存配置失败: {e}")

    def _load_config(self):
        """加载配置"""
        from PyQt5.QtWidgets import QInputDialog, QMessageBox

        # TODO: 实现从已保存模板列表选择
        templates = ["默认配置", "工资表标准配置", "异动表标准配置"]

        template, ok = QInputDialog.getItem(
            self, "加载配置", "选择要加载的配置模板:", templates, 0, False
        )

        if ok and template:
            try:
                # TODO: 实现配置加载逻辑
                self.status_label.setText(f"✅ 已加载配置模板: {template}")
                self.status_label.setStyleSheet("color: green; font-weight: bold;")

                QMessageBox.information(
                    self, "加载成功",
                    f"已加载配置模板: {template}"
                )

            except Exception as e:
                self.status_label.setText(f"❌ 加载失败: {str(e)}")
                self.status_label.setStyleSheet("color: red; font-weight: bold;")
                self.logger.error(f"加载配置失败: {e}")

    def _add_custom_rule(self):
        """添加自定义规则"""
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLineEdit, QPushButton, QTextEdit, QLabel

        dialog = QDialog(self)
        dialog.setWindowTitle("添加自定义处理规则")
        dialog.setMinimumSize(400, 300)

        layout = QVBoxLayout(dialog)

        # 规则名称
        layout.addWidget(QLabel("规则名称:"))
        name_edit = QLineEdit()
        layout.addWidget(name_edit)

        # 规则描述
        layout.addWidget(QLabel("规则描述:"))
        desc_edit = QTextEdit()
        desc_edit.setMaximumHeight(100)
        layout.addWidget(desc_edit)

        # 按钮
        button_layout = QHBoxLayout()
        ok_btn = QPushButton("确定")
        cancel_btn = QPushButton("取消")

        ok_btn.clicked.connect(dialog.accept)
        cancel_btn.clicked.connect(dialog.reject)

        button_layout.addWidget(ok_btn)
        button_layout.addWidget(cancel_btn)
        layout.addLayout(button_layout)

        if dialog.exec_() == QDialog.Accepted:
            rule_name = name_edit.text().strip()
            rule_desc = desc_edit.toPlainText().strip()

            if rule_name:
                self.rules_list.addItem(f"{rule_name}: {rule_desc}")
                self.status_label.setText(f"✅ 已添加自定义规则: {rule_name}")
                self.status_label.setStyleSheet("color: green; font-weight: bold;")

    def _edit_custom_rule(self):
        """编辑自定义规则"""
        current_item = self.rules_list.currentItem()
        if current_item:
            # TODO: 实现规则编辑逻辑
            self.status_label.setText("⚠️ 规则编辑功能待完善")
            self.status_label.setStyleSheet("color: orange; font-weight: bold;")
        else:
            self.status_label.setText("⚠️ 请先选择要编辑的规则")
            self.status_label.setStyleSheet("color: orange; font-weight: bold;")

    def _remove_custom_rule(self):
        """删除自定义规则"""
        current_row = self.rules_list.currentRow()
        if current_row >= 0:
            from PyQt5.QtWidgets import QMessageBox
            reply = QMessageBox.question(
                self, "确认删除",
                "确定要删除选中的自定义规则吗？",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                self.rules_list.takeItem(current_row)
                self.status_label.setText("✅ 自定义规则已删除")
                self.status_label.setStyleSheet("color: green; font-weight: bold;")
        else:
            self.status_label.setText("⚠️ 请先选择要删除的规则")
            self.status_label.setStyleSheet("color: orange; font-weight: bold;")

    def get_processing_config(self):
        """获取当前处理配置"""
        self._update_processing_config()
        return self.processing_config.copy()


class PreviewValidationWidget(QWidget):
    """预览验证选项卡组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = setup_logger(__name__)
        self.current_sheet_name = ""
        self._init_ui()
    
    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 预览信息头
        info_layout = QHBoxLayout()
        self.sheet_label = QLabel("当前工作表: 未选择")
        self.sheet_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        self.record_count_label = QLabel("记录数: 0")
        
        info_layout.addWidget(self.sheet_label)
        info_layout.addStretch()
        info_layout.addWidget(self.record_count_label)
        layout.addLayout(info_layout)
        
        # 预览数据表格
        self.preview_table = QTableWidget()
        self.preview_table.setAlternatingRowColors(True)
        self.preview_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.preview_table.setSelectionMode(QTableWidget.SingleSelection)
        layout.addWidget(self.preview_table, 1)
        
        # 验证结果区域
        validation_group = QGroupBox("验证结果")
        validation_layout = QVBoxLayout(validation_group)
        
        # 验证状态
        status_layout = QHBoxLayout()
        self.validation_status_label = QLabel("验证状态: 未验证")
        self.validation_status_label.setStyleSheet("font-weight: bold;")
        
        self.validate_data_btn = QPushButton("🔍 验证数据")
        self.validate_data_btn.setMinimumHeight(30)
        
        status_layout.addWidget(self.validation_status_label)
        status_layout.addStretch()
        status_layout.addWidget(self.validate_data_btn)
        validation_layout.addLayout(status_layout)
        
        # 错误信息列表
        self.error_list = QTreeWidget()
        self.error_list.setHeaderLabels(["类型", "字段", "行号", "错误信息"])
        self.error_list.setMaximumHeight(150)
        validation_layout.addWidget(self.error_list)
        
        layout.addWidget(validation_group)
        
        # 连接信号
        self.validate_data_btn.clicked.connect(self._validate_current_data)
    
    def show_preview_data(self, sheet_name: str, data: List[Dict]):
        """显示预览数据"""
        try:
            self.current_sheet_name = sheet_name
            self.sheet_label.setText(f"当前工作表: {sheet_name}")
            self.record_count_label.setText(f"记录数: {len(data)}")
            
            if not data:
                self.preview_table.setRowCount(0)
                self.preview_table.setColumnCount(0)
                return
            
            # 设置表格结构
            headers = list(data[0].keys())
            self.preview_table.setColumnCount(len(headers))
            self.preview_table.setHorizontalHeaderLabels(headers)
            self.preview_table.setRowCount(len(data))
            
            # 填充数据
            for row, record in enumerate(data):
                for col, header in enumerate(headers):
                    value = record.get(header, "")
                    item = QTableWidgetItem(str(value) if value is not None else "")
                    item.setFlags(item.flags() & ~Qt.ItemIsEditable)  # 只读
                    self.preview_table.setItem(row, col, item)
            
            # 自动调整列宽
            self.preview_table.resizeColumnsToContents()
            
            # 重置验证状态
            self.validation_status_label.setText("验证状态: 未验证")
            self.validation_status_label.setStyleSheet("font-weight: bold; color: #666;")
            self.error_list.clear()
            
            self.logger.info(f"预览数据加载完成: {sheet_name} - {len(data)} 行")
            
        except Exception as e:
            self.logger.error(f"显示预览数据失败: {e}")
    
    def _validate_current_data(self):
        """验证当前预览的数据"""
        try:
            if not self.current_sheet_name:
                self.validation_status_label.setText("验证状态: 无数据可验证")
                return
            
            self.validation_status_label.setText("验证状态: 验证中...")
            self.validation_status_label.setStyleSheet("font-weight: bold; color: #ff9800;")
            
            # TODO: 实现数据验证逻辑
            # 这里应该获取当前的映射配置，然后验证数据
            
            # 模拟验证结果
            self.error_list.clear()
            
            # 示例：添加一些验证结果
            from PyQt5.QtWidgets import QTreeWidgetItem
            
            warning_item = QTreeWidgetItem(["警告", "姓名", "3", "字段值可能包含特殊字符"])
            warning_item.setIcon(0, self.style().standardIcon(self.style().SP_MessageBoxWarning))
            self.error_list.addTopLevelItem(warning_item)
            
            error_item = QTreeWidgetItem(["错误", "工资", "5", "数据类型不匹配，期望数字类型"])
            error_item.setIcon(0, self.style().standardIcon(self.style().SP_MessageBoxCritical))
            self.error_list.addTopLevelItem(error_item)
            
            # 更新验证状态
            error_count = self.error_list.topLevelItemCount()
            if error_count == 0:
                self.validation_status_label.setText("验证状态: ✅ 通过")
                self.validation_status_label.setStyleSheet("font-weight: bold; color: green;")
            else:
                self.validation_status_label.setText(f"验证状态: ❌ 发现 {error_count} 个问题")
                self.validation_status_label.setStyleSheet("font-weight: bold; color: red;")
            
            self.logger.info(f"数据验证完成: {self.current_sheet_name} - {error_count} 个问题")
            
        except Exception as e:
            self.validation_status_label.setText("验证状态: 验证失败")
            self.validation_status_label.setStyleSheet("font-weight: bold; color: red;")
            self.logger.error(f"数据验证失败: {e}")


# 测试用主函数
if __name__ == "__main__":
    import sys
    from PyQt5.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyle('Fusion')
    
    window = UnifiedDataImportWindow()
    window.show()
    
    sys.exit(app.exec_())
